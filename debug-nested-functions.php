<?php
/**
 * Debug script for nested function calculation
 *
 * This script helps debug nested function calculation issues in the Price Form Builder plugin.
 * Place this file in the plugin directory and access it via the browser.
 */

// Load WordPress
require_once( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/wp-load.php' );

// Check if user is logged in and has admin privileges
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'You do not have sufficient permissions to access this page.' );
}

// Include required files
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-calculator.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-simple-calculator.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-formula-parser.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test nested function calculation
function test_nested_function_calculation() {
    // Test formulas with nested functions
    $formulas = array(
        'Simple max' => 'max(0,{X}-5000)',
        'Simple floor' => 'floor({X}/1000)',
        'Simple nested' => 'floor(max(0,{X}-5000))',
        'Complex nested' => '(floor(max(0,{X}-5000)/1000))*2',
        'Very complex' => '(floor(max(0,{X}-5000)/1000))*{Z}',
    );

    // Test values
    $test_values = array(
        array('X' => 1000, 'Y' => 1000, 'Z' => 2),
        array('X' => 3000, 'Y' => 1000, 'Z' => 2),
        array('X' => 5000, 'Y' => 1000, 'Z' => 2),
        array('X' => 6000, 'Y' => 1000, 'Z' => 2),
        array('X' => 7500, 'Y' => 1000, 'Z' => 2),
        array('X' => 10000, 'Y' => 1000, 'Z' => 3),
    );

    echo '<h1>Nested Function Calculation Debug</h1>';

    echo '<h2>Test Formulas</h2>';
    echo '<ul>';
    foreach ($formulas as $name => $formula) {
        echo '<li><strong>' . $name . ':</strong> ' . htmlspecialchars($formula) . '</li>';
    }
    echo '</ul>';

    echo '<h2>Test Calculations with Simple Calculator</h2>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>X Value</th><th>Y Value</th><th>Z Value</th>';
    foreach ($formulas as $name => $formula) {
        echo '<th>' . $name . '</th>';
    }
    echo '<th>Manual Calculation</th></tr>';

    // Initialize calculator
    $calculator = new PFB_Simple_Calculator(array(), true);

    foreach ($test_values as $values) {
        echo '<tr>';
        echo '<td>' . $values['X'] . '</td>';
        echo '<td>' . $values['Y'] . '</td>';
        echo '<td>' . $values['Z'] . '</td>';

        foreach ($formulas as $name => $formula) {
            $result = $calculator->calculate($formula, $values);
            echo '<td>' . $result . '</td>';
        }

        // Manual calculation of the complex nested formula
        $manual_result = 0;
        $x = $values['X'];
        $z = $values['Z'];
        $diff = max(0, $x - 5000);
        $divided = $diff / 1000;
        $floored = floor($divided);
        $final = $floored * $z;
        
        echo '<td>' . $final . '</td>';
        echo '</tr>';
    }
    echo '</table>';

    echo '<h2>Test Calculations with Formula Parser</h2>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>X Value</th><th>Y Value</th><th>Z Value</th>';
    foreach ($formulas as $name => $formula) {
        echo '<th>' . $name . '</th>';
    }
    echo '<th>Manual Calculation</th></tr>';

    foreach ($test_values as $values) {
        echo '<tr>';
        echo '<td>' . $values['X'] . '</td>';
        echo '<td>' . $values['Y'] . '</td>';
        echo '<td>' . $values['Z'] . '</td>';

        foreach ($formulas as $name => $formula) {
            $parser = new PFB_Formula_Parser(array(), $values, true);
            $result = $parser->evaluate($formula);
            echo '<td>' . $result . '</td>';
        }

        // Manual calculation of the complex nested formula
        $manual_result = 0;
        $x = $values['X'];
        $z = $values['Z'];
        $diff = max(0, $x - 5000);
        $divided = $diff / 1000;
        $floored = floor($divided);
        $final = $floored * $z;
        
        echo '<td>' . $final . '</td>';
        echo '</tr>';
    }
    echo '</table>';

    echo '<h2>Debug Log</h2>';
    echo '<pre>';
    $log_file = ini_get('error_log');
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $pfb_log_lines = array_filter($log_lines, function($line) {
            return strpos($line, '[PFB') !== false;
        });
        echo implode("\n", array_slice($pfb_log_lines, -50));
    } else {
        echo "Log file not found or not accessible.";
    }
    echo '</pre>';
}

// Run the test
test_nested_function_calculation();
