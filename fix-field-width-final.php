<?php
/**
 * Fix field width values in the database.
 *
 * This script directly updates the field_width values in the database to ensure they are properly formatted.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

echo "<h1>Fix Field Width Values (Final)</h1>";

// Check if the field_width column exists
global $wpdb;
$table_name = $wpdb->prefix . 'pfb_form_fields';

// Check if the table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
if (!$table_exists) {
    die("<p>Table {$table_name} does not exist!</p>");
}

echo "<p>Table {$table_name} exists.</p>";

// Check if the field_width column exists
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
if (empty($column_exists)) {
    echo "<p>Column 'field_width' does not exist in table {$table_name}.</p>";
    
    // Add the column
    echo "<p>Attempting to add the column...</p>";
    $result = $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN field_width varchar(10) DEFAULT '100' AFTER field_required");
    
    if ($result === false) {
        die("<p>Failed to add field_width column. Error: " . $wpdb->last_error . "</p>");
    } else {
        echo "<p>Successfully added field_width column.</p>";
    }
} else {
    echo "<p>Column 'field_width' exists in table {$table_name}.</p>";
}

// Get all fields
$fields = $wpdb->get_results("SELECT * FROM {$table_name}", ARRAY_A);

if (empty($fields)) {
    die("<p>No fields found in the database.</p>");
}

echo "<p>Found " . count($fields) . " fields in the database.</p>";

// Fix field width values
echo "<h2>Fixing Field Width Values</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Form ID</th><th>Type</th><th>Label</th><th>Name</th><th>Old Width</th><th>New Width</th><th>Result</th></tr>";

$fixed_count = 0;

foreach ($fields as $field) {
    $old_width = $field['field_width'];
    $new_width = $old_width;
    
    // Remove % symbol if present
    $new_width = str_replace('%', '', $new_width);
    
    // Ensure it's a valid numeric value
    if (!is_numeric($new_width)) {
        $new_width = '100';
    }
    
    // Check if the width needs to be updated
    if ($old_width !== $new_width) {
        // Update the field_width column
        $result = $wpdb->update(
            $table_name,
            array('field_width' => $new_width),
            array('id' => $field['id'])
        );
        
        $status = ($result !== false) ? 'Success' : 'Failed - ' . $wpdb->last_error;
        $fixed_count++;
    } else {
        $status = 'No change needed';
    }
    
    echo "<tr>";
    echo "<td>{$field['id']}</td>";
    echo "<td>{$field['form_id']}</td>";
    echo "<td>{$field['field_type']}</td>";
    echo "<td>{$field['field_label']}</td>";
    echo "<td>{$field['field_name']}</td>";
    echo "<td>{$old_width}</td>";
    echo "<td>{$new_width}</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<p>Fixed {$fixed_count} field width values.</p>";

// Also check for width in field_options and remove it
echo "<h2>Checking for Width in Field Options</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Form ID</th><th>Type</th><th>Label</th><th>Name</th><th>Width in Options</th><th>Result</th></tr>";

$options_fixed_count = 0;

foreach ($fields as $field) {
    $options = maybe_unserialize($field['field_options']);
    $width_in_options = isset($options['width']) ? $options['width'] : 'N/A';
    
    if (isset($options['width'])) {
        // Remove width from options
        unset($options['width']);
        $serialized_options = maybe_serialize($options);
        
        // Update the field_options column
        $result = $wpdb->update(
            $table_name,
            array('field_options' => $serialized_options),
            array('id' => $field['id'])
        );
        
        $status = ($result !== false) ? 'Success' : 'Failed - ' . $wpdb->last_error;
        $options_fixed_count++;
    } else {
        $status = 'No width in options';
    }
    
    echo "<tr>";
    echo "<td>{$field['id']}</td>";
    echo "<td>{$field['form_id']}</td>";
    echo "<td>{$field['field_type']}</td>";
    echo "<td>{$field['field_label']}</td>";
    echo "<td>{$field['field_name']}</td>";
    echo "<td>{$width_in_options}</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<p>Removed width from {$options_fixed_count} field options.</p>";

echo "<p>Done! <a href='admin.php?page=pfb-form-editor'>Go back to Form Editor</a></p>";
