<?php
/**
 * Update RTL Currency Symbol Position
 *
 * This script adds the rtl_symbol_position column to the currencies table.
 */

// Load WordPress
require_once dirname(__FILE__) . '/../../../wp-load.php';

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have sufficient permissions to access this page.');
}

// Include the update script
require_once 'includes/update-rtl-currency.php';

// Run the update function
$result = pfb_add_rtl_symbol_position_column();

// Output the result
echo '<h1>RTL Currency Symbol Position Update</h1>';
echo '<p>Update result: ' . ($result ? 'Success' : 'Failed') . '</p>';

// Check if the column exists after update
global $wpdb;
$table_name = $wpdb->prefix . 'pfb_currencies';
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'rtl_symbol_position'");
echo '<p>Column rtl_symbol_position exists after update: ' . (!empty($column_exists) ? 'Yes' : 'No') . '</p>';

// Show current currencies with their symbol positions
$currencies = $wpdb->get_results("SELECT id, name, code, symbol, symbol_position, rtl_symbol_position FROM {$table_name}", ARRAY_A);

echo '<h2>Current Currencies</h2>';
if (!empty($currencies)) {
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Symbol</th><th>Symbol Position</th><th>RTL Symbol Position</th></tr>';
    
    foreach ($currencies as $currency) {
        echo '<tr>';
        echo '<td>' . esc_html($currency['id']) . '</td>';
        echo '<td>' . esc_html($currency['name']) . '</td>';
        echo '<td>' . esc_html($currency['code']) . '</td>';
        echo '<td>' . esc_html($currency['symbol']) . '</td>';
        echo '<td>' . esc_html($currency['symbol_position']) . '</td>';
        echo '<td>' . esc_html($currency['rtl_symbol_position'] ?: 'Not set') . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No currencies found.</p>';
}

echo '<p>Done!</p>';
echo '<p><a href="' . admin_url('admin.php?page=pfb-currencies') . '">Return to Currencies</a></p>';
