/**
 * Enhanced subtotal styles for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */

/* Base Subtotal Field Styles */
.pfb-field-subtotal {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e0e0;
}

/* Subtotal Header Styles */
.pfb-subtotal-header {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    position: relative;
    color: #333;
}

.pfb-subtotal-header:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #ddd, transparent);
}

/* Subtotal Lines Container */
.pfb-subtotal-lines {
    margin-bottom: 15px;
}

/* Individual Subtotal Line */
.pfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
    margin-bottom: 5px;
}

.pfb-subtotal-line:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Subtotal Line Label */
.pfb-subtotal-line-label {
    flex: 1;
    font-size: 14px;
    color: #555;
    padding-right: 15px;
}

.pfb-subtotal-line-label:before {
    content: '•';
    margin-right: 8px;
    color: #999;
}

/* Subtotal Line Value */
.pfb-subtotal-line-value {
    font-size: 14px;
    font-weight: 700; /* Make numbers bold */
    color: #333;
    min-width: 80px;
    text-align: right;
}

/* Force LTR for numeric values */
.pfb-subtotal-line-value[dir="ltr"],
.pfb-subtotal-value[dir="ltr"] {
    direction: ltr;
    unicode-bidi: isolate;
}

/* Subtotal Total Section */
.pfb-subtotal-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    margin-top: 10px;
    position: relative;
    font-weight: 600;
}

.pfb-subtotal-total:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, #ddd, #ddd);
}

/* Subtotal Total Label */
.pfb-subtotal-total-label {
    font-size: 16px;
    color: #333;
}

/* Subtotal Total Value */
.pfb-subtotal-value {
    font-size: 16px;
    color: #333;
    min-width: 80px;
    text-align: right;
}

/* Empty Value Placeholder */
.pfb-subtotal-line-value.empty,
.pfb-subtotal-value.empty {
    color: #999;
}

/* RTL Specific Subtotal Styles */
.rtl .pfb-subtotal-line,
.is-rtl .pfb-subtotal-line {
    flex-direction: row-reverse;
    justify-content: space-between;
}

.rtl .pfb-subtotal-line-label,
.is-rtl .pfb-subtotal-line-label {
    text-align: right;
    padding-left: 15px;
    padding-right: 0;
}

.rtl .pfb-subtotal-line-label:before,
.is-rtl .pfb-subtotal-line-label:before {
    margin-right: 0;
    margin-left: 8px;
    float: right;
}

.rtl .pfb-subtotal-line-value,
.is-rtl .pfb-subtotal-line-value {
    text-align: left;
    direction: ltr;
}

.rtl .pfb-subtotal-total,
.is-rtl .pfb-subtotal-total {
    flex-direction: row-reverse;
    justify-content: space-between;
}

.rtl .pfb-subtotal-total-label,
.is-rtl .pfb-subtotal-total-label {
    text-align: right;
}

.rtl .pfb-subtotal-value,
.is-rtl .pfb-subtotal-value {
    text-align: left;
    direction: ltr;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .pfb-field-subtotal {
        padding: 15px;
    }

    .pfb-subtotal-header {
        font-size: 16px;
    }

    .pfb-subtotal-line-label,
    .pfb-subtotal-line-value {
        font-size: 13px;
    }

    .pfb-subtotal-total-label,
    .pfb-subtotal-value {
        font-size: 15px;
    }
}
