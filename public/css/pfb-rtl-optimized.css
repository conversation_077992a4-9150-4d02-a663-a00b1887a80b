/**
 * RTL styles for the Price Form Builder plugin.
 * Optimized version with reduced duplication and improved organization.
 *
 * @since      1.0.0
 */

/* ======= General RTL Styles ======= */
.rtl .pfb-form-container,
.is-rtl.pfb-form-container {
    direction: rtl;
}

.rtl .pfb-form-header,
.is-rtl .pfb-form-header {
    text-align: right;
}

.rtl .pfb-form-description,
.is-rtl .pfb-form-description {
    text-align: right;
}

/* ======= Form Fields RTL Styles ======= */
.rtl .pfb-field-label,
.is-rtl .pfb-field-label {
    text-align: right;
}

.rtl .pfb-field-required,
.is-rtl .pfb-field-required {
    margin-left: 0;
    margin-right: 3px;
}

/* Force LTR for numeric inputs but align text to the right for RTL */
.rtl .pfb-field-input,
.rtl .pfb-field-textarea,
.is-rtl .pfb-field-input,
.is-rtl .pfb-field-textarea {
    direction: ltr;
    text-align: right;
}

/* ======= Special handling for select fields in RTL mode ======= */
.rtl .pfb-field-select,
.is-rtl .pfb-field-select {
    direction: rtl;
    text-align: right;
    background-position: left 15px center !important;
    padding-right: 15px !important;
    padding-left: 40px !important;
}

/* Ensure dropdown options are right-aligned in RTL mode */
.rtl .pfb-field-select option,
.is-rtl .pfb-field-select option {
    direction: rtl;
    text-align: right;
}

/* ======= Radio and Checkbox RTL Styles ======= */
.rtl .pfb-radio-label,
.rtl .pfb-checkbox-label,
.is-rtl .pfb-radio-label,
.is-rtl .pfb-checkbox-label {
    padding-left: 0;
    padding-right: 30px;
    text-align: right;
}

.rtl .pfb-radio-input,
.rtl .pfb-checkbox-input,
.is-rtl .pfb-radio-input,
.is-rtl .pfb-checkbox-input {
    margin-right: 0;
    margin-left: 12px;
    right: 0;
    left: auto;
}

/* ======= Error Messages RTL Styles ======= */
.rtl .pfb-field-error,
.is-rtl .pfb-field-error {
    text-align: right;
}

.rtl .pfb-field-error:before,
.is-rtl .pfb-field-error:before {
    margin-right: 0;
    margin-left: 6px;
    float: right;
}

/* ======= Currency Selector RTL Styles ======= */
.rtl .pfb-currency-selector,
.is-rtl .pfb-currency-selector {
    flex-direction: row-reverse;
}

.rtl .pfb-currency-label,
.is-rtl .pfb-currency-label {
    margin-right: 0;
    margin-left: 12px;
}

/* ======= Subtotal Field RTL Styles ======= */
.rtl .pfb-subtotal-header,
.is-rtl .pfb-subtotal-header {
    text-align: right;
}

.rtl .pfb-subtotal-header:after,
.is-rtl .pfb-subtotal-header:after {
    left: auto;
    right: 50%;
    transform: translateX(50%);
}

/* ======= Subtotal Lines RTL Styles ======= */
.rtl .pfb-subtotal-line,
.is-rtl .pfb-subtotal-line {
    flex-direction: row-reverse;
    text-align: right;
    justify-content: space-between;
    display: flex;
}

.rtl .pfb-subtotal-line-label,
.is-rtl .pfb-subtotal-line-label {
    text-align: right;
    padding-left: 15px;
    padding-right: 0;
}

.rtl .pfb-subtotal-line-label:before,
.is-rtl .pfb-subtotal-line-label:before {
    margin-right: 0;
    margin-left: 8px;
    float: right;
}

.rtl .pfb-subtotal-line-value,
.is-rtl .pfb-subtotal-line-value {
    text-align: left;
    direction: ltr;
}

/* ======= Subtotal Total RTL Styles ======= */
.rtl .pfb-subtotal-total,
.is-rtl .pfb-subtotal-total {
    flex-direction: row-reverse;
    justify-content: space-between;
    display: flex;
}

.rtl .pfb-subtotal-total:before,
.is-rtl .pfb-subtotal-total:before {
    left: auto;
    right: 0;
}

.rtl .pfb-subtotal-total-label,
.is-rtl .pfb-subtotal-total-label {
    text-align: right;
    padding-left: 15px;
    padding-right: 0;
}

.rtl .pfb-subtotal-value,
.is-rtl .pfb-subtotal-value {
    text-align: left;
    direction: ltr;
}

/* ======= Total Field RTL Styles ======= */
.rtl .pfb-field-total,
.is-rtl .pfb-field-total {
    text-align: right;
}

.rtl .pfb-total-label,
.is-rtl .pfb-total-label {
    text-align: right;
}

.rtl .pfb-total-value,
.is-rtl .pfb-total-value {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

/* ======= Form Footer RTL Styles ======= */
.rtl .pfb-form-footer,
.is-rtl .pfb-form-footer {
    flex-direction: row-reverse;
}

/* ======= Calculate Button RTL Styles ======= */
.rtl .pfb-calculate-button,
.is-rtl .pfb-calculate-button {
    text-align: center;
}

/* ======= Responsive RTL Adjustments ======= */
@media (max-width: 768px) {
    .rtl .pfb-form-footer,
    .is-rtl .pfb-form-footer {
        flex-direction: column;
    }

    .rtl .pfb-currency-selector,
    .is-rtl .pfb-currency-selector {
        width: 100%;
        justify-content: space-between;
    }
}
