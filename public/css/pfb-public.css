/**
 * Public styles for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */

/* Reset styles to prevent theme conflicts */
.pfb-form-container * {
    box-sizing: border-box;
}

.pfb-form-container input,
.pfb-form-container select,
.pfb-form-container textarea,
.pfb-form-container button {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    line-height: normal;
}

/* General Styles */
.pfb-form-container {
    font-family: 'Poppins', sans-serif;
    max-width: 800px;
    margin: 0 auto 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.pfb-form-header {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    padding: 25px 35px;
    position: relative;
}

.pfb-form-header:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
}

.pfb-form-header h2 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: #fff;
    letter-spacing: -0.5px;
}

.pfb-form-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
    line-height: 1.5;
}

.pfb-form-content {
    padding: 35px;
}

/* Form layout for field width options */
.pfb-form {
    margin-left: -15px;
    margin-right: -15px;
    width: 100%;
}

/* Clearfix for form */
.pfb-form:after {
    content: "";
    display: table;
    clear: both;
}

.pfb-form-footer {
    background: #f9f9f9;
    padding: 20px 35px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

/* Calculate Button */
.pfb-calculate-button {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 10px rgba(142, 84, 233, 0.3);
    animation: pfb-pulse 2s infinite;
    display: inline-block;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    line-height: 1.5;
}

.pfb-calculate-button:hover {
    background: linear-gradient(135deg, #3665D5 0%, #7D43D8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4);
}

.pfb-calculate-button.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.pfb-calculate-button.loading:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: pfb-spin 0.8s linear infinite;
}

@keyframes pfb-spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pfb-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(142, 84, 233, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(142, 84, 233, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(142, 84, 233, 0);
    }
}

/* Form Elements */
.pfb-field {
    margin-bottom: 30px;
    position: relative;
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
    float: left;
    width: 100%;
}

/* Field width classes */
.pfb-field-width-100 {
    width: 100%;
}

.pfb-field-width-50 {
    width: 50%;
}

.pfb-field-width-33 {
    width: 33.333%;
}

.pfb-field-width-25 {
    width: 25%;
}

/* Responsive adjustments for field widths */
@media (max-width: 768px) {
    .pfb-field-width-50,
    .pfb-field-width-33,
    .pfb-field-width-25 {
        width: 100%;
    }
}

.pfb-field-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.pfb-field-required {
    color: #ff4d4d;
    margin-left: 3px;
}

.pfb-field-help {
    margin-top: 6px;
    font-size: 13px;
    color: #777;
}

.pfb-field-input {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    transition: all 0.3s ease;
    background-color: #fcfcfc;
    /* Force LTR direction for input values */
    direction: ltr;
    text-align: left;
}

.pfb-field-input:focus {
    border-color: #8E54E9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.15);
    background-color: #fff;
}

/* Slider field styles */
.pfb-slider-container {
    padding: 10px 0;
}

.pfb-slider-input {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    outline: none;
    margin: 15px 0;
}

.pfb-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(142, 84, 233, 0.4);
    transition: all 0.2s ease;
}

.pfb-slider-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(142, 84, 233, 0.4);
    transition: all 0.2s ease;
}

.pfb-slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(142, 84, 233, 0.6);
}

.pfb-slider-input::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(142, 84, 233, 0.6);
}

.pfb-slider-value-display {
    text-align: center;
    font-weight: 600;
    font-size: 18px;
    color: #8E54E9;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 8px 15px;
    display: inline-block;
    min-width: 60px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
}

.pfb-slider-current-value {
    position: relative;
    display: inline-block;
}

.pfb-slider-current-value:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #f5f5f5;
}

.pfb-field-select {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    appearance: none;
    background-color: #fcfcfc;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
    transition: all 0.3s ease;
    /* Force LTR direction for select values */
    direction: ltr;
    text-align: left;
}

.pfb-field-select:focus {
    border-color: #8E54E9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.15);
    background-color: #fff;
}

.pfb-field-radio,
.pfb-field-checkbox {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.pfb-radio-item,
.pfb-checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background-color: #fcfcfc;
    border: 2px solid #e0e0e0;
}

.pfb-radio-item:hover,
.pfb-checkbox-item:hover {
    background-color: #f5f5f5;
}

.pfb-radio-input,
.pfb-checkbox-input {
    margin-right: 12px;
    cursor: pointer;
    width: 18px;
    height: 18px;
}

.pfb-radio-label,
.pfb-checkbox-label {
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
}

/* Subtotal Field Styles */
.pfb-field-subtotal {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(224, 224, 224, 0.7);
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.pfb-field-subtotal:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.07), 0 1px 5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.pfb-subtotal-header {
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    padding-bottom: 12px;
}

.pfb-subtotal-header:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 3px;
}

.pfb-subtotal-label {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    letter-spacing: -0.5px;
}

.pfb-subtotal-lines {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07);
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
}

.pfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 2px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.pfb-subtotal-line:nth-child(odd) {
    background-color: rgba(248, 249, 250, 0.7);
}

.pfb-subtotal-line:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.7);
}

.pfb-subtotal-line:hover {
    background-color: rgba(142, 84, 233, 0.05);
}

.pfb-subtotal-line:last-child {
    margin-bottom: 0;
}

.pfb-subtotal-line-label {
    font-size: 15px;
    font-weight: 500;
    color: #444;
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.pfb-subtotal-line-label:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

.pfb-subtotal-line-value {
    font-size: 16px;
    font-weight: 600;
    color: #8E54E9;
    min-width: 100px;
    text-align: right;
    background: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
}

.pfb-subtotal-line:hover .pfb-subtotal-line-value {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pfb-subtotal-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin-top: 10px;
    background: linear-gradient(135deg, rgba(71, 118, 230, 0.05) 0%, rgba(142, 84, 233, 0.1) 100%);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.pfb-subtotal-total:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(to bottom, #4776E6 0%, #8E54E9 100%);
}

.pfb-subtotal-total-label {
    font-size: 17px;
    font-weight: 700;
    color: #333;
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.pfb-subtotal-value {
    font-size: 22px;
    font-weight: 700;
    color: #8E54E9;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    min-width: 120px;
    text-align: right;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pfb-subtotal-value:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* Enhanced Subtotal Field Styles */
.pfb-field-subtotal {
    background: #ffffff;
    border-radius: 16px;
    padding: 28px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06), 0 1px 5px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(224, 224, 224, 0.5);
    transition: all 0.3s ease;
}

.pfb-field-subtotal:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08), 0 2px 10px rgba(0, 0, 0, 0.04);
    transform: translateY(-3px);
}

.pfb-subtotal-header {
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.pfb-subtotal-header:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 4px;
}

.pfb-subtotal-label {
    font-size: 22px;
    font-weight: 800;
    color: #333;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

.pfb-subtotal-lines {
    margin-bottom: 25px;
    padding: 5px 0 15px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
}

.pfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 18px;
    margin-bottom: 4px;
    border-radius: 8px;
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
}

.pfb-subtotal-line:nth-child(odd) {
    background-color: rgba(248, 249, 250, 0.9);
}

.pfb-subtotal-line:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.9);
}

.pfb-subtotal-line:hover {
    background-color: rgba(142, 84, 233, 0.08);
    transform: translateY(-1px);
}

.pfb-subtotal-line-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.pfb-subtotal-line-label:before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
    box-shadow: 0 1px 3px rgba(142, 84, 233, 0.3);
}

.pfb-subtotal-line-value {
    font-size: 17px;
    font-weight: 700;
    color: #8E54E9;
    min-width: 120px;
    text-align: right;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.25s ease;
    border: 1px solid rgba(142, 84, 233, 0.1);
}

.pfb-subtotal-line:hover .pfb-subtotal-line-value {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    border-color: rgba(142, 84, 233, 0.2);
}

.pfb-subtotal-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 22px;
    margin-top: 15px;
    background: linear-gradient(135deg, rgba(71, 118, 230, 0.08) 0%, rgba(142, 84, 233, 0.12) 100%);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(142, 84, 233, 0.15);
}

.pfb-subtotal-total:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background: linear-gradient(to bottom, #4776E6 0%, #8E54E9 100%);
    box-shadow: 0 0 10px rgba(142, 84, 233, 0.3);
}

.pfb-subtotal-total-label {
    font-size: 18px;
    font-weight: 800;
    color: #333;
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.pfb-subtotal-value {
    font-size: 24px;
    font-weight: 800;
    color: #8E54E9;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
    min-width: 140px;
    text-align: right;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 18px;
    border-radius: 10px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(142, 84, 233, 0.15);
}

.pfb-subtotal-value:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: rgba(142, 84, 233, 0.25);
}

/* RTL Support for Subtotal Fields */
.pfb-subtotal-line.pfb-rtl {
    flex-direction: row-reverse;
}

.pfb-subtotal-total.pfb-rtl {
    flex-direction: row-reverse;
}

/* Force LTR for numeric values */
.pfb-subtotal-line-value[dir="ltr"],
.pfb-subtotal-value[dir="ltr"] {
    direction: ltr;
    text-align: right;
    unicode-bidi: isolate;
}

/* Enhanced RTL Support for Subtotal Fields */
.rtl .pfb-subtotal-line {
    flex-direction: row-reverse;
}

.rtl .pfb-subtotal-total {
    flex-direction: row-reverse;
}

.rtl .pfb-subtotal-total:before {
    left: auto;
    right: 0;
}

.rtl .pfb-subtotal-line-label {
    padding-right: 15px;
    padding-left: 0;
    text-align: right;
}

.rtl .pfb-subtotal-line-label:before {
    margin-right: 0;
    margin-left: 10px;
}

.rtl .pfb-subtotal-total-label {
    padding-right: 15px;
    padding-left: 0;
    text-align: right;
}

/* Ensure proper alignment for RTL mode */
.rtl .pfb-subtotal-line-value,
.rtl .pfb-subtotal-value {
    text-align: left;
}

/* Total Field Styles */
.pfb-field-total {
    background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 2px solid #e0e0e0;
}

.pfb-total-label {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.pfb-total-value {
    font-size: 32px;
    font-weight: 700;
    color: #8E54E9;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
}

/* Buttons */
.pfb-btn {
    display: inline-block;
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.pfb-btn-primary {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    box-shadow: 0 4px 10px rgba(142, 84, 233, 0.3);
}

.pfb-btn-primary:hover {
    background: linear-gradient(135deg, #3665D5 0%, #7D43D8 100%);
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4);
    transform: translateY(-2px);
}

.pfb-btn-secondary {
    background: #f5f5f5;
    color: #333;
    border: 2px solid #e0e0e0;
}

.pfb-btn-secondary:hover {
    background: #e9e9e9;
    border-color: #d0d0d0;
}

/* Currency Selector */
.pfb-currency-selector {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 2px solid #e0e0e0;
}

.pfb-currency-label {
    margin-right: 12px;
    font-size: 15px;
    font-weight: 500;
    color: #555;
}

.pfb-currency-select {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 500;
    appearance: none;
    background-color: #fcfcfc;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    transition: all 0.3s ease;
    min-width: 180px;
}

.pfb-currency-select:focus {
    border-color: #8E54E9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.15);
    background-color: #fff;
}

/* Validation */
.pfb-field-error {
    color: #ff4d4d;
    font-size: 13px;
    margin-top: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.pfb-field-error:before {
    content: '⚠️';
    margin-right: 6px;
    font-size: 14px;
}

.pfb-field-input.error,
.pfb-field-select.error {
    border-color: #ff4d4d;
    background-color: #fff9f9;
}

/* Loading */
.pfb-total-value.loading {
    position: relative;
    color: transparent;
}

.pfb-total-value.loading:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid rgba(142, 84, 233, 0.2);
    border-radius: 50%;
    border-top-color: #8E54E9;
    animation: pfb-spin 1s ease-in-out infinite;
}

@keyframes pfb-spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Animation */
.pfb-field {
    animation: pfb-fade-in 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
}

@keyframes pfb-fade-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Apply staggered animation to fields */
.pfb-field:nth-child(1) { animation-delay: 0.1s; }
.pfb-field:nth-child(2) { animation-delay: 0.2s; }
.pfb-field:nth-child(3) { animation-delay: 0.3s; }
.pfb-field:nth-child(4) { animation-delay: 0.4s; }
.pfb-field:nth-child(5) { animation-delay: 0.5s; }
.pfb-field:nth-child(6) { animation-delay: 0.6s; }
.pfb-field:nth-child(7) { animation-delay: 0.7s; }
.pfb-field:nth-child(8) { animation-delay: 0.8s; }
.pfb-field:nth-child(9) { animation-delay: 0.9s; }
.pfb-field:nth-child(10) { animation-delay: 1s; }

/* Responsive */
@media (max-width: 768px) {
    .pfb-form-header,
    .pfb-form-content,
    .pfb-form-footer {
        padding: 20px;
    }

    .pfb-form-header h2 {
        font-size: 24px;
    }

    .pfb-form-footer {
        flex-direction: column;
        gap: 15px;
    }

    .pfb-currency-selector {
        width: 100%;
        justify-content: space-between;
    }

    .pfb-total-value {
        font-size: 28px;
    }
}

/* Hidden Fields */
.pfb-field-hidden {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    height: 0 !important;
    width: 0 !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* RTL Support */
.rtl .pfb-form-container {
    direction: rtl;
}

.rtl .pfb-field-label {
    text-align: right;
}

.rtl .pfb-field-required {
    margin-left: 0;
    margin-right: 3px;
}

.rtl .pfb-field-input,
.rtl .pfb-field-select {
    /* Keep input values LTR */
    direction: ltr;
    text-align: left;
}

.rtl .pfb-radio-input,
.rtl .pfb-checkbox-input {
    margin-right: 0;
    margin-left: 12px;
}

.rtl .pfb-field-error:before {
    margin-right: 0;
    margin-left: 6px;
}

.rtl .pfb-currency-label {
    margin-right: 0;
    margin-left: 12px;
}

/* RTL Support for Subtotal Fields */
.rtl .pfb-subtotal-line {
    flex-direction: row-reverse;
}

.rtl .pfb-subtotal-total {
    flex-direction: row-reverse;
}

.rtl .pfb-subtotal-total:before {
    left: auto;
    right: 0;
}

/* RTL specific adjustments for subtotal fields */
.rtl .pfb-subtotal-line-label {
    padding-right: 0;
    padding-left: 15px;
    text-align: right;
}

.rtl .pfb-subtotal-line-label:before {
    margin-right: 0;
    margin-left: 8px;
}

.rtl .pfb-subtotal-total-label {
    padding-right: 0;
    padding-left: 15px;
    text-align: right;
}

/* Keep numeric values LTR even in RTL mode, but align them to the left in RTL mode */
.rtl .pfb-subtotal-line-value,
.rtl .pfb-subtotal-value,
.rtl .pfb-total-value {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

/* Animation for value changes */
@keyframes pfb-value-change {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pfb-value-changed {
    animation: pfb-value-change 0.5s ease;
}