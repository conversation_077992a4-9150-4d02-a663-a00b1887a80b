/**
 * LTR styles for the Price Form Builder plugin.
 * Optimized version with reduced duplication and improved organization.
 *
 * @since      1.0.0
 */

/* ======= General LTR Styles ======= */
.pfb-form-container,
.is-ltr.pfb-form-container {
    direction: ltr;
}

.pfb-form-header,
.is-ltr .pfb-form-header {
    text-align: left;
}

.pfb-form-description,
.is-ltr .pfb-form-description {
    text-align: left;
}

/* ======= Form Fields LTR Styles ======= */
.pfb-field-label,
.is-ltr .pfb-field-label {
    text-align: left;
}

.pfb-field-required,
.is-ltr .pfb-field-required {
    margin-right: 3px;
    margin-left: 0;
}

.pfb-field-input,
.pfb-field-select,
.pfb-field-textarea,
.is-ltr .pfb-field-input,
.is-ltr .pfb-field-select,
.is-ltr .pfb-field-textarea {
    direction: ltr;
    text-align: left;
}

/* ======= Radio and Checkbox LTR Styles ======= */
.pfb-radio-label,
.pfb-checkbox-label,
.is-ltr .pfb-radio-label,
.is-ltr .pfb-checkbox-label {
    padding-left: 30px;
    padding-right: 0;
    text-align: left;
}

.pfb-radio-input,
.pfb-checkbox-input,
.is-ltr .pfb-radio-input,
.is-ltr .pfb-checkbox-input {
    margin-right: 12px;
    margin-left: 0;
    left: 0;
    right: auto;
}

/* ======= Error Messages LTR Styles ======= */
.pfb-field-error,
.is-ltr .pfb-field-error {
    text-align: left;
}

.pfb-field-error:before,
.is-ltr .pfb-field-error:before {
    margin-right: 6px;
    margin-left: 0;
    float: left;
}

/* ======= Currency Selector LTR Styles ======= */
.pfb-currency-selector,
.is-ltr .pfb-currency-selector {
    flex-direction: row;
}

.pfb-currency-label,
.is-ltr .pfb-currency-label {
    margin-right: 12px;
    margin-left: 0;
}

/* ======= Subtotal Field LTR Styles ======= */
.pfb-subtotal-header,
.is-ltr .pfb-subtotal-header {
    text-align: left;
}

.pfb-subtotal-header:after,
.is-ltr .pfb-subtotal-header:after {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
}

/* ======= Subtotal Lines LTR Styles ======= */
.pfb-subtotal-line,
.is-ltr .pfb-subtotal-line {
    flex-direction: row;
    text-align: left;
    justify-content: space-between;
}

.pfb-subtotal-line-label,
.is-ltr .pfb-subtotal-line-label {
    text-align: left;
    padding-right: 15px;
    padding-left: 0;
}

.pfb-subtotal-line-label:before,
.is-ltr .pfb-subtotal-line-label:before {
    margin-right: 8px;
    margin-left: 0;
    float: left;
}

.pfb-subtotal-line-value,
.is-ltr .pfb-subtotal-line-value {
    text-align: right;
    direction: ltr;
}

/* ======= Subtotal Total LTR Styles ======= */
.pfb-subtotal-total,
.is-ltr .pfb-subtotal-total {
    flex-direction: row;
    justify-content: space-between;
}

.pfb-subtotal-total:before,
.is-ltr .pfb-subtotal-total:before {
    left: 0;
    right: auto;
}

.pfb-subtotal-total-label,
.is-ltr .pfb-subtotal-total-label {
    text-align: left;
    padding-right: 15px;
    padding-left: 0;
}

.pfb-subtotal-value,
.is-ltr .pfb-subtotal-value {
    text-align: right;
    direction: ltr;
}

/* ======= Total Field LTR Styles ======= */
.pfb-field-total,
.is-ltr .pfb-field-total {
    text-align: left;
}

.pfb-total-label,
.is-ltr .pfb-total-label {
    text-align: left;
}

.pfb-total-value,
.is-ltr .pfb-total-value {
    direction: ltr;
    display: inline-block;
    text-align: right;
}

/* ======= Form Footer LTR Styles ======= */
.pfb-form-footer,
.is-ltr .pfb-form-footer {
    flex-direction: row;
}

/* ======= Calculate Button LTR Styles ======= */
.pfb-calculate-button,
.is-ltr .pfb-calculate-button {
    text-align: center;
}

/* ======= Responsive LTR Adjustments ======= */
@media (max-width: 768px) {
    .pfb-form-footer,
    .is-ltr .pfb-form-footer {
        flex-direction: column;
    }

    .pfb-currency-selector,
    .is-ltr .pfb-currency-selector {
        width: 100%;
        justify-content: space-between;
    }
}
