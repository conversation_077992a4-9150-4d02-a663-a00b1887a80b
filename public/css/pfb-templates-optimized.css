/**
 * Form templates for the Price Form Builder plugin.
 * Optimized version with reduced duplication and improved organization.
 *
 * @since      1.0.0
 */

/* ======= Template 1: Default (Modern) ======= */
.pfb-template-default {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 30px;
}

.pfb-template-default .pfb-form-header {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    padding: 25px 35px;
    position: relative;
}

.pfb-template-default .pfb-form-header:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
}

.pfb-template-default .pfb-form-header h2 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: #fff;
    letter-spacing: -0.5px;
}

.pfb-template-default .pfb-form-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
    line-height: 1.5;
}

.pfb-template-default .pfb-form-content {
    padding: 35px;
    background: #fff;
}

.pfb-template-default .pfb-form-footer {
    padding: 20px 35px;
    background: #f9f9f9;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.pfb-template-default .pfb-field {
    margin-bottom: 20px;
}

.pfb-template-default .pfb-field-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.pfb-template-default .pfb-field-input,
.pfb-template-default .pfb-field-select,
.pfb-template-default .pfb-field-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.pfb-template-default .pfb-field-input:focus,
.pfb-template-default .pfb-field-select:focus,
.pfb-template-default .pfb-field-textarea:focus {
    border-color: #8E54E9;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.15);
    outline: none;
}

/* Dropdown styling */
.pfb-template-default .pfb-field-select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23666" d="M0 0l6 6 6-6z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.pfb-template-default .pfb-calculate-button {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.pfb-template-default .pfb-calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(142, 84, 233, 0.3);
}

/* ======= Template 2: Minimal ======= */
.pfb-template-minimal {
    box-shadow: none;
    border: 1px solid #e0e0e0;
    border-radius: 0;
    background-color: #fff;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.pfb-template-minimal .pfb-form-header {
    background: #f7f7f7;
    color: #333;
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
    text-align: center;
}

.pfb-template-minimal .pfb-form-header h2 {
    font-size: 22px;
    font-weight: 400;
    margin: 0 0 8px 0;
    color: #333;
    letter-spacing: 0;
}

.pfb-template-minimal .pfb-form-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.pfb-template-minimal .pfb-form-content {
    padding: 25px;
}

.pfb-template-minimal .pfb-form-footer {
    padding: 15px 25px;
    background: #f7f7f7;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfb-template-minimal .pfb-field {
    margin-bottom: 15px;
}

.pfb-template-minimal .pfb-field-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 400;
    color: #555;
    font-size: 14px;
}

.pfb-template-minimal .pfb-field-input,
.pfb-template-minimal .pfb-field-select,
.pfb-template-minimal .pfb-field-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 0;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: #fafafa;
}

.pfb-template-minimal .pfb-field-input:focus,
.pfb-template-minimal .pfb-field-select:focus,
.pfb-template-minimal .pfb-field-textarea:focus {
    border-color: #999;
    box-shadow: none;
    background-color: #fff;
}

/* Dropdown styling */
.pfb-template-minimal .pfb-field-select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23666" d="M0 0l6 6 6-6z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 30px;
}

/* Radio and checkbox styling */
.pfb-template-minimal .pfb-radio-item,
.pfb-template-minimal .pfb-checkbox-item {
    margin-bottom: 5px;
}

.pfb-template-minimal .pfb-radio-label,
.pfb-template-minimal .pfb-checkbox-label {
    font-size: 14px;
    color: #555;
}

/* Calculate button */
.pfb-template-minimal .pfb-calculate-button {
    background: #555;
    color: #fff;
    border: none;
    border-radius: 0;
    padding: 8px 16px;
    font-weight: 400;
    font-size: 14px;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pfb-template-minimal .pfb-calculate-button:hover {
    background: #333;
}

/* ======= Template 3: Material Design ======= */
.pfb-template-professional {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08), 0 4px 20px rgba(0, 0, 0, 0.06);
    border: none;
    border-radius: 4px;
    background-color: #fff;
    font-family: 'Roboto', 'Segoe UI', sans-serif;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.pfb-template-professional:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), 0 8px 25px rgba(0, 0, 0, 0.08);
}

.pfb-template-professional .pfb-form-header {
    background: #6200ee; /* Material Design primary color */
    color: #fff;
    padding: 24px 32px;
    position: relative;
}

.pfb-template-professional .pfb-form-header h2 {
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #fff;
    letter-spacing: 0;
}

.pfb-template-professional .pfb-form-header p {
    margin: 0;
    opacity: 0.87;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
}

.pfb-template-professional .pfb-form-content {
    padding: 32px;
    background: #fff;
}

.pfb-template-professional .pfb-form-footer {
    padding: 16px 32px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfb-template-professional .pfb-field {
    margin-bottom: 24px;
    position: relative;
}

.pfb-template-professional .pfb-field-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #212121;
    font-size: 14px;
    transition: color 0.2s ease;
}

/* Material Design inspired form fields */
.pfb-template-professional .pfb-field-input,
.pfb-template-professional .pfb-field-select,
.pfb-template-professional .pfb-field-textarea {
    width: 100%;
    padding: 12px 16px;
    border: none;
    border-bottom: 2px solid #e0e0e0;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
}

.pfb-template-professional .pfb-field-input:focus,
.pfb-template-professional .pfb-field-select:focus,
.pfb-template-professional .pfb-field-textarea:focus {
    border-bottom-color: #6200ee;
    box-shadow: none;
    background-color: #f9f5ff;
    outline: none;
}

/* Dropdown styling */
.pfb-template-professional .pfb-field-select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23666" d="M0 0l6 6 6-6z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 16px center;
    padding-right: 40px;
}

/* Calculate button */
.pfb-template-professional .pfb-calculate-button {
    background: #6200ee;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(98, 0, 238, 0.2);
}

.pfb-template-professional .pfb-calculate-button:hover {
    background: #7722ff;
    box-shadow: 0 4px 8px rgba(98, 0, 238, 0.3);
    transform: translateY(-1px);
}
