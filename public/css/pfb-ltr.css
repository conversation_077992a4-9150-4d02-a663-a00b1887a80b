/**
 * LTR styles for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */

/* General LTR Styles */
.pfb-form-container {
    direction: ltr;
}

.pfb-form-header {
    text-align: left;
}

.pfb-form-description {
    text-align: left;
}

/* Form Fields LTR Styles */
.pfb-field-label {
    text-align: left;
}

.pfb-field-required {
    margin-right: 3px;
    margin-left: 0;
}

.pfb-field-input,
.pfb-field-select,
.pfb-field-textarea {
    direction: ltr;
    text-align: left;
}

/* Radio and Checkbox LTR Styles */
.pfb-radio-label,
.pfb-checkbox-label {
    padding-left: 30px;
    padding-right: 0;
    text-align: left;
}

.pfb-radio-input,
.pfb-checkbox-input {
    margin-right: 12px;
    margin-left: 0;
    left: 0;
    right: auto;
}

/* Error Messages LTR Styles */
.pfb-field-error {
    text-align: left;
}

.pfb-field-error:before {
    margin-right: 6px;
    margin-left: 0;
    float: left;
}

/* Currency Selector LTR Styles */
.pfb-currency-selector {
    flex-direction: row;
}

.pfb-currency-label {
    margin-right: 12px;
    margin-left: 0;
}

/* Enhanced Subtotal Field LTR Styles */
.pfb-subtotal-header {
    text-align: left;
}

.pfb-subtotal-header:after {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
}

/* Subtotal Lines LTR Styles */
.pfb-subtotal-line,
.is-ltr .pfb-subtotal-line {
    flex-direction: row;
    text-align: left;
    justify-content: space-between;
}

.pfb-subtotal-line-label,
.is-ltr .pfb-subtotal-line-label {
    text-align: left;
    padding-right: 15px;
    padding-left: 0;
}

.pfb-subtotal-line-label:before,
.is-ltr .pfb-subtotal-line-label:before {
    margin-right: 8px;
    margin-left: 0;
    float: left;
}

.pfb-subtotal-line-value,
.is-ltr .pfb-subtotal-line-value {
    text-align: right;
    direction: ltr;
}

/* Subtotal Total LTR Styles */
.pfb-subtotal-total,
.is-ltr .pfb-subtotal-total {
    flex-direction: row;
    justify-content: space-between;
}

.pfb-subtotal-total:before,
.is-ltr .pfb-subtotal-total:before {
    left: 0;
    right: auto;
}

.pfb-subtotal-total-label,
.is-ltr .pfb-subtotal-total-label {
    text-align: left;
    padding-right: 15px;
    padding-left: 0;
}

.pfb-subtotal-value,
.is-ltr .pfb-subtotal-value {
    text-align: right;
    direction: ltr;
}

/* Total Field LTR Styles */
.pfb-field-total {
    text-align: left;
}

.pfb-total-label {
    text-align: left;
}

.pfb-total-value {
    direction: ltr;
    display: inline-block;
    text-align: right;
}

/* Form Footer LTR Styles */
.pfb-form-footer {
    flex-direction: row;
}

/* Calculate Button LTR Styles */
.pfb-calculate-button {
    text-align: center;
}

/* Responsive LTR Adjustments */
@media (max-width: 768px) {
    .pfb-form-footer {
        flex-direction: column;
    }

    .pfb-currency-selector {
        width: 100%;
        justify-content: space-between;
    }
}
