/**
 * Template-specific subtotal styles for the Price Form Builder plugin.
 * Optimized version with reduced duplication and improved organization.
 *
 * @since      1.0.0
 */

/* ======= Template 1: Default (Modern) - Subtotal Styles ======= */
.pfb-template-default .pfb-field-subtotal {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #eaeaea;
}

.pfb-template-default .pfb-subtotal-header {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    color: #333;
    border-bottom: 1px solid #eaeaea;
}

.pfb-template-default .pfb-subtotal-line {
    padding: 8px 0;
}

.pfb-template-default .pfb-subtotal-line-label {
    font-size: 14px;
    color: #555;
}

.pfb-template-default .pfb-subtotal-line-value {
    font-size: 14px;
    color: #333;
}

.pfb-template-default .pfb-subtotal-total {
    padding-top: 10px;
    margin-top: 10px;
    border-top: 1px solid #eaeaea;
}

.pfb-template-default .pfb-subtotal-total-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.pfb-template-default .pfb-subtotal-value {
    font-size: 16px;
    color: #333;
}

/* ======= Template 2: Minimal - Subtotal Styles ======= */
.pfb-template-minimal .pfb-field-subtotal {
    background-color: #f7f7f7;
    border: 1px solid #e0e0e0;
    border-radius: 0;
    padding: 15px;
}

.pfb-template-minimal .pfb-subtotal-header {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
}

.pfb-template-minimal .pfb-subtotal-line {
    padding: 5px 0;
}

.pfb-template-minimal .pfb-subtotal-line-label {
    font-size: 13px;
}

.pfb-template-minimal .pfb-subtotal-line-value {
    font-size: 13px;
}

.pfb-template-minimal .pfb-subtotal-total {
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid #e0e0e0;
}

.pfb-template-minimal .pfb-subtotal-total-label {
    font-size: 14px;
    font-weight: 500;
}

.pfb-template-minimal .pfb-subtotal-value {
    font-size: 14px;
}

/* ======= Template 3: Material Design - Subtotal Styles ======= */
.pfb-template-professional .pfb-field-subtotal {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
}

.pfb-template-professional .pfb-field-subtotal:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.pfb-template-professional .pfb-subtotal-header {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
    padding-bottom: 12px;
    color: #212121;
    border-bottom: 1px solid #e0e0e0;
}

.pfb-template-professional .pfb-subtotal-line {
    padding: 8px 0;
    transition: background-color 0.2s ease;
}

.pfb-template-professional .pfb-subtotal-line:hover {
    background-color: rgba(98, 0, 238, 0.03);
}

.pfb-template-professional .pfb-subtotal-line-label {
    font-size: 14px;
    color: #616161;
}

.pfb-template-professional .pfb-subtotal-line-value {
    font-size: 14px;
    font-weight: 500;
    color: #212121;
}

.pfb-template-professional .pfb-subtotal-total {
    padding-top: 12px;
    margin-top: 12px;
    border-top: 2px solid #e0e0e0;
}

.pfb-template-professional .pfb-subtotal-total-label {
    font-size: 16px;
    font-weight: 500;
    color: #212121;
}

.pfb-template-professional .pfb-subtotal-value {
    font-size: 16px;
    font-weight: 700;
    color: #6200ee;
}
