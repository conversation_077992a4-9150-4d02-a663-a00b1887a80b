/**
 * Frontend Conditional Logic for Price Form Builder
 *
 * This file handles conditional logic evaluation and field visibility
 * on the frontend forms.
 *
 * @since      1.0.2
 */

(function($) {
    'use strict';

    /**
     * Conditional Logic Manager
     */
    var ConditionalLogic = {
        
        /**
         * Form conditional logic rules
         */
        rules: {},
        
        /**
         * Initialize conditional logic
         */
        init: function() {
            console.log('PFB Conditional Logic: Initializing...');
            
            // Load conditional logic rules from form data
            this.loadRules();
            
            // Bind events
            this.bindEvents();
            
            // Initial evaluation
            this.evaluateAll();
            
            console.log('PFB Conditional Logic: Initialized with rules:', this.rules);
        },
        
        /**
         * Load conditional logic rules from the form
         */
        loadRules: function() {
            var self = this;
            
            // Check if conditional logic data is available
            if (typeof pfb_conditional_logic !== 'undefined') {
                self.rules = pfb_conditional_logic;
                console.log('PFB Conditional Logic: Loaded rules from global variable');
            } else {
                // Try to load from form data attributes
                $('.pfb-form').each(function() {
                    var $form = $(this);
                    var rulesData = $form.data('conditional-logic');
                    
                    if (rulesData) {
                        self.rules = rulesData;
                        console.log('PFB Conditional Logic: Loaded rules from form data attribute');
                    }
                });
            }
        },
        
        /**
         * Bind events for form field changes
         */
        bindEvents: function() {
            var self = this;
            
            // Listen for changes on all form inputs
            $(document).on('change input', '.pfb-form input, .pfb-form select, .pfb-form textarea', function() {
                console.log('PFB Conditional Logic: Field changed:', $(this).attr('name'));
                self.evaluateAll();
            });
            
            // Listen for checkbox changes specifically
            $(document).on('change', '.pfb-form input[type="checkbox"]', function() {
                console.log('PFB Conditional Logic: Checkbox changed:', $(this).attr('name'));
                self.evaluateAll();
            });
            
            // Listen for radio button changes
            $(document).on('change', '.pfb-form input[type="radio"]', function() {
                console.log('PFB Conditional Logic: Radio changed:', $(this).attr('name'));
                self.evaluateAll();
            });
        },
        
        /**
         * Evaluate all conditional logic rules
         */
        evaluateAll: function() {
            var self = this;
            
            if (!self.rules || Object.keys(self.rules).length === 0) {
                return;
            }
            
            // Get current form data
            var formData = self.getFormData();
            
            // Evaluate each field's conditions
            $.each(self.rules, function(fieldName, rule) {
                self.evaluateField(fieldName, rule, formData);
            });
        },
        
        /**
         * Get current form data
         */
        getFormData: function() {
            var formData = {};
            
            $('.pfb-form').find('input, select, textarea').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var type = $field.attr('type');
                
                if (!name) return;
                
                if (type === 'checkbox') {
                    if (!formData[name]) {
                        formData[name] = [];
                    }
                    if ($field.is(':checked')) {
                        formData[name].push($field.val());
                    }
                } else if (type === 'radio') {
                    if ($field.is(':checked')) {
                        formData[name] = $field.val();
                    }
                } else {
                    formData[name] = $field.val();
                }
            });
            
            return formData;
        },
        
        /**
         * Evaluate conditions for a specific field
         */
        evaluateField: function(fieldName, rule, formData) {
            var self = this;
            
            if (!rule.conditions || rule.conditions.length === 0) {
                return;
            }
            
            console.log('PFB Conditional Logic: Evaluating field:', fieldName, 'with rule:', rule);
            
            var results = [];
            
            // Evaluate each condition
            $.each(rule.conditions, function(index, condition) {
                var result = self.evaluateCondition(condition, formData);
                results.push(result);
                
                console.log('PFB Conditional Logic: Condition result:', condition, '=', result);
            });
            
            // Apply logical operator
            var finalResult;
            if (rule.operator === 'or') {
                finalResult = results.indexOf(true) !== -1; // At least one true
            } else {
                finalResult = results.indexOf(false) === -1; // All true
            }
            
            console.log('PFB Conditional Logic: Final result for', fieldName, ':', finalResult);
            
            // Apply action
            self.applyAction(fieldName, rule.action, finalResult);
        },
        
        /**
         * Evaluate a single condition
         */
        evaluateCondition: function(condition, formData) {
            var fieldValue = formData[condition.field] || '';
            var conditionValue = condition.value || '';
            var operator = condition.operator || 'equals';
            
            // Handle array values (for checkboxes)
            if (Array.isArray(fieldValue)) {
                fieldValue = fieldValue.join(',');
            }
            
            // Convert to strings for comparison
            fieldValue = String(fieldValue).trim();
            conditionValue = String(conditionValue).trim();
            
            switch (operator) {
                case 'equals':
                    return fieldValue === conditionValue;
                    
                case 'not_equals':
                    return fieldValue !== conditionValue;
                    
                case 'greater_than':
                    return parseFloat(fieldValue) > parseFloat(conditionValue);
                    
                case 'less_than':
                    return parseFloat(fieldValue) < parseFloat(conditionValue);
                    
                case 'greater_equal':
                    return parseFloat(fieldValue) >= parseFloat(conditionValue);
                    
                case 'less_equal':
                    return parseFloat(fieldValue) <= parseFloat(conditionValue);
                    
                case 'contains':
                    return fieldValue.toLowerCase().indexOf(conditionValue.toLowerCase()) !== -1;
                    
                case 'not_contains':
                    return fieldValue.toLowerCase().indexOf(conditionValue.toLowerCase()) === -1;
                    
                case 'starts_with':
                    return fieldValue.toLowerCase().indexOf(conditionValue.toLowerCase()) === 0;
                    
                case 'ends_with':
                    return fieldValue.toLowerCase().lastIndexOf(conditionValue.toLowerCase()) === 
                           fieldValue.length - conditionValue.length;
                    
                case 'is_empty':
                    return fieldValue === '';
                    
                case 'is_not_empty':
                    return fieldValue !== '';
                    
                case 'in_array':
                    var arrayValues = conditionValue.split(',').map(function(v) { return v.trim(); });
                    return arrayValues.indexOf(fieldValue) !== -1;
                    
                case 'not_in_array':
                    var arrayValues = conditionValue.split(',').map(function(v) { return v.trim(); });
                    return arrayValues.indexOf(fieldValue) === -1;
                    
                default:
                    return false;
            }
        },
        
        /**
         * Apply action to field based on condition result
         */
        applyAction: function(fieldName, action, conditionMet) {
            var $field = $('.pfb-form').find('[name="' + fieldName + '"]').closest('.pfb-form-group');
            
            if ($field.length === 0) {
                console.warn('PFB Conditional Logic: Field not found:', fieldName);
                return;
            }
            
            console.log('PFB Conditional Logic: Applying action', action, 'to field', fieldName, 'condition met:', conditionMet);
            
            switch (action) {
                case 'show':
                    if (conditionMet) {
                        $field.show().removeClass('pfb-hidden-by-logic');
                    } else {
                        $field.hide().addClass('pfb-hidden-by-logic');
                    }
                    break;
                    
                case 'hide':
                    if (conditionMet) {
                        $field.hide().addClass('pfb-hidden-by-logic');
                    } else {
                        $field.show().removeClass('pfb-hidden-by-logic');
                    }
                    break;
                    
                case 'enable':
                    var $input = $field.find('input, select, textarea');
                    if (conditionMet) {
                        $input.prop('disabled', false).removeClass('pfb-disabled-by-logic');
                    } else {
                        $input.prop('disabled', true).addClass('pfb-disabled-by-logic');
                    }
                    break;
                    
                case 'disable':
                    var $input = $field.find('input, select, textarea');
                    if (conditionMet) {
                        $input.prop('disabled', true).addClass('pfb-disabled-by-logic');
                    } else {
                        $input.prop('disabled', false).removeClass('pfb-disabled-by-logic');
                    }
                    break;
                    
                case 'require':
                    var $input = $field.find('input, select, textarea');
                    if (conditionMet) {
                        $input.prop('required', true).addClass('pfb-required-by-logic');
                        $field.find('label').addClass('pfb-required');
                    } else {
                        $input.prop('required', false).removeClass('pfb-required-by-logic');
                        $field.find('label').removeClass('pfb-required');
                    }
                    break;
                    
                case 'optional':
                    var $input = $field.find('input, select, textarea');
                    if (conditionMet) {
                        $input.prop('required', false).removeClass('pfb-required-by-logic');
                        $field.find('label').removeClass('pfb-required');
                    } else {
                        $input.prop('required', true).addClass('pfb-required-by-logic');
                        $field.find('label').addClass('pfb-required');
                    }
                    break;
            }
            
            // Trigger custom event for other scripts to listen to
            $(document).trigger('pfb_conditional_logic_applied', {
                fieldName: fieldName,
                action: action,
                conditionMet: conditionMet,
                field: $field
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        ConditionalLogic.init();
    });
    
    // Make ConditionalLogic available globally for debugging
    window.PFB_ConditionalLogic = ConditionalLogic;
    
})(jQuery);
