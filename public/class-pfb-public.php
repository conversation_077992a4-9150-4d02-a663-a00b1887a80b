<?php
/**
 * The public-facing functionality of the plugin.
 *
 * @since      1.0.0
 */
class PFB_Public {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of the plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        // Only load styles when the shortcode is used
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'price_form')) {
            // Enqueue the main CSS file
            wp_enqueue_style($this->plugin_name, PFB_PLUGIN_URL . 'public/css/pfb-public.css', array(), $this->version, 'all');

            // Enqueue the enhanced subtotal CSS
            wp_enqueue_style('pfb-subtotal', PFB_PLUGIN_URL . 'public/css/pfb-subtotal.css', array($this->plugin_name), $this->version, 'all');

            // Enqueue the templates CSS
            wp_enqueue_style('pfb-templates', PFB_PLUGIN_URL . 'public/css/pfb-templates.css', array($this->plugin_name), $this->version, 'all');

            // Check if the site is RTL or LTR
            if (is_rtl()) {
                // Enqueue RTL specific styles
                wp_enqueue_style('pfb-rtl', PFB_PLUGIN_URL . 'public/css/pfb-rtl.css', array($this->plugin_name), $this->version, 'all');
            } else {
                // Enqueue LTR specific styles
                wp_enqueue_style('pfb-ltr', PFB_PLUGIN_URL . 'public/css/pfb-ltr.css', array($this->plugin_name), $this->version, 'all');
            }

            // Add Google Fonts with a more specific handle to avoid conflicts
            wp_enqueue_style('pfb-google-fonts', 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap', array(), $this->version);
        }
    }

    /**
     * Register the JavaScript for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        // Only load scripts when the shortcode is used
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'price_form')) {
            wp_enqueue_script($this->plugin_name, PFB_PLUGIN_URL . 'public/js/pfb-public.js', array('jquery'), $this->version, true);

            // Enqueue conditional logic script
            wp_enqueue_script($this->plugin_name . '-conditional-logic', PFB_PLUGIN_URL . 'public/js/pfb-conditional-logic.js', array('jquery'), $this->version, true);

            wp_localize_script($this->plugin_name, 'pfb_data', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('pfb_public_nonce'),
                'version' => $this->version,
                'i18n' => array(
                    'thank_you' => __('Thank you for your submission!', 'price-form-builder'),
                    'required_field' => __('This field is required.', 'price-form-builder'),
                    'invalid_number' => __('Please enter a valid number.', 'price-form-builder'),
                    'calculating' => __('Calculating...', 'price-form-builder')
                )
            ));
        }
    }

    /**
     * Render the price form.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Form HTML.
     */
    public function render_price_form($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'title' => true,
            'currency' => '',
            'language' => ''
        ), $atts, 'price_form');

        if (empty($atts['id'])) {
            return '<p>' . __('Please specify a form ID.', 'price-form-builder') . '</p>';
        }

        $form = new PFB_Form();

        if (!$form->load($atts['id'])) {
            return '<p>' . __('Form not found.', 'price-form-builder') . '</p>';
        }

        return $form->render($atts);
    }

    /**
     * AJAX handler for calculating price.
     *
     * @since    1.0.0
     */
    public function ajax_calculate_price() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_public_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Get form ID
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

        if (!$form_id) {
            wp_send_json_error(array('message' => __('No form ID provided.', 'price-form-builder')));
        }

        // Get form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

        if (empty($form_data)) {
            wp_send_json_error(array('message' => __('No form data provided.', 'price-form-builder')));
        }

        // Debug log the form data
        error_log('Form data for calculation: ' . print_r($form_data, true));

        // Sanitize form data
        $sanitized_form_data = array();
        foreach ($form_data as $key => $value) {
            if (is_array($value)) {
                $sanitized_values = array();
                foreach ($value as $item) {
                    $sanitized_values[] = sanitize_text_field($item);
                }
                $sanitized_form_data[$key] = $sanitized_values;
            } else {
                $sanitized_form_data[$key] = sanitize_text_field($value);
            }
        }

        // Get currency
        $currency_code = isset($_POST['currency']) ? sanitize_text_field($_POST['currency']) : '';
        $currency = null;

        if (!empty($currency_code)) {
            $currency = new PFB_Currency();
            $currency->load_by_code($currency_code);
        } else {
            $currency = new PFB_Currency();
        }

        // Load form
        $form = new PFB_Form();

        if (!$form->load($form_id)) {
            wp_send_json_error(array('message' => __('Form not found.', 'price-form-builder')));
        }

        try {
            // Calculate price
            $result = $form->calculate_price_with_subtotals($sanitized_form_data);
            $price = $result['total'];
            $subtotals = $result['subtotals'];

            // Format price - check if we're in RTL mode
            $is_rtl = is_rtl();
            $formatted_price = $currency->format_price($price, $is_rtl);

            // Debug log the calculation result
            error_log('Calculation result: ' . print_r($result, true));

            // Format subtotals - handle both simple values and structured subtotals with lines
            $formatted_subtotals = array();

            foreach ($subtotals as $field_name => $subtotal) {
                // Check if this is a multi-line subtotal
                if (is_array($subtotal) && isset($subtotal['lines'])) {
                    // Keep the structure but format the numeric values
                    $formatted_subtotals[$field_name] = $subtotal;

                    // Format the total
                    $formatted_subtotals[$field_name]['total'] = $subtotal['total'];

                    // Format each line value
                    foreach ($subtotal['lines'] as $index => $line) {
                        $formatted_subtotals[$field_name]['lines'][$index]['value'] = $line['value'];
                    }
                } else {
                    // Legacy single-value subtotal
                    $formatted_subtotals[$field_name] = $subtotal;
                }
            }

            // Include variables in the response if available
            $variables = isset($result['variables']) ? $result['variables'] : array();

            // Debug log for currency
            error_log('PFB: Currency data: ' . print_r(array(
                'code' => $currency->get_code(),
                'exchange_rate' => $currency->get_exchange_rate(),
                'original_price' => $price,
                'formatted_price' => $formatted_price
            ), true));

            wp_send_json_success(array(
                'price' => $price,
                'formatted_price' => $formatted_price,
                'subtotals' => $formatted_subtotals,
                'variables' => $variables,
                'currency' => array(
                    'symbol' => $currency->get_symbol(),
                    'code' => $currency->get_code(),
                    'symbol_position' => $currency->get_symbol_position(),
                    'rtl_symbol_position' => $currency->get_rtl_symbol_position(),
                    'thousand_separator' => $currency->get_thousand_separator(),
                    'decimal_separator' => $currency->get_decimal_separator(),
                    'decimal_places' => $currency->get_decimal_places(),
                    'exchange_rate' => $currency->get_exchange_rate()
                )
            ));
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Error calculating price: ', 'price-form-builder') . $e->getMessage(),
                'data' => $sanitized_form_data
            ));
        }
    }
}
