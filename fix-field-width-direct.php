<?php
/**
 * Fix field width values in the database.
 *
 * This script directly updates the field_width values in the database.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

echo "<h1>Fix Field Width Values (Direct)</h1>";

// Get form ID from query string
$form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;

if (!$form_id) {
    die('No form ID provided. Please add ?form_id=X to the URL.');
}

echo "<p>Fixing form ID: {$form_id}</p>";

// Check if the field_width column exists
global $wpdb;
$table_name = $wpdb->prefix . 'pfb_form_fields';

// Check if the table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
if (!$table_exists) {
    die("<p>Table {$table_name} does not exist!</p>");
}

echo "<p>Table {$table_name} exists.</p>";

// Check if the field_width column exists
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
if (empty($column_exists)) {
    echo "<p>Column 'field_width' does not exist in table {$table_name}.</p>";
    
    // Add the column
    echo "<p>Attempting to add the column...</p>";
    $result = $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN field_width varchar(10) DEFAULT '100' AFTER field_required");
    
    if ($result === false) {
        die("<p>Failed to add field_width column. Error: " . $wpdb->last_error . "</p>");
    } else {
        echo "<p>Successfully added field_width column.</p>";
    }
} else {
    echo "<p>Column 'field_width' exists in table {$table_name}.</p>";
}

// Get fields for the form
$fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$table_name} WHERE form_id = %d", $form_id), ARRAY_A);

if (empty($fields)) {
    die("<p>No fields found for form ID {$form_id}.</p>");
}

echo "<p>Found " . count($fields) . " fields for form ID {$form_id}.</p>";

// Show current field widths
echo "<h2>Current Field Widths</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Type</th><th>Label</th><th>Name</th><th>Width</th><th>Options</th></tr>";

foreach ($fields as $field) {
    $options = maybe_unserialize($field['field_options']);
    $width_in_options = isset($options['width']) ? $options['width'] : 'N/A';
    
    echo "<tr>";
    echo "<td>{$field['id']}</td>";
    echo "<td>{$field['field_type']}</td>";
    echo "<td>{$field['field_label']}</td>";
    echo "<td>{$field['field_name']}</td>";
    echo "<td>{$field['field_width']}</td>";
    echo "<td>Width in options: {$width_in_options}</td>";
    echo "</tr>";
}

echo "</table>";

// Set a specific field width
if (isset($_GET['set_width']) && isset($_GET['field_id'])) {
    $field_id = intval($_GET['field_id']);
    $width = $_GET['set_width'];
    
    // Remove % symbol if present
    $width = str_replace('%', '', $width);
    
    // Ensure it's a valid numeric value
    if (!is_numeric($width)) {
        $width = '100';
    }
    
    // Update the field_width column
    $result = $wpdb->update(
        $table_name,
        array('field_width' => $width),
        array('id' => $field_id)
    );
    
    echo "<p>Updated field ID {$field_id} width to {$width}: " . ($result !== false ? 'Success' : 'Failed - ' . $wpdb->last_error) . "</p>";
    
    // Also update the field_options to remove any width setting
    $field = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $field_id), ARRAY_A);
    if ($field) {
        $options = maybe_unserialize($field['field_options']);
        
        if (is_array($options) && isset($options['width'])) {
            unset($options['width']);
            $serialized_options = maybe_serialize($options);
            
            $result = $wpdb->update(
                $table_name,
                array('field_options' => $serialized_options),
                array('id' => $field_id)
            );
            
            echo "<p>Removed width from options for field ID {$field_id}: " . ($result !== false ? 'Success' : 'Failed - ' . $wpdb->last_error) . "</p>";
        }
    }
    
    echo "<p>Field width updated. <a href='?form_id={$form_id}'>Refresh</a> to see the changes.</p>";
}

// Form to set field widths
echo "<h2>Set Field Widths</h2>";
echo "<form method='get'>";
echo "<input type='hidden' name='form_id' value='{$form_id}'>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Type</th><th>Label</th><th>Name</th><th>Current Width</th><th>New Width</th><th>Action</th></tr>";

foreach ($fields as $field) {
    echo "<tr>";
    echo "<td>{$field['id']}</td>";
    echo "<td>{$field['field_type']}</td>";
    echo "<td>{$field['field_label']}</td>";
    echo "<td>{$field['field_name']}</td>";
    echo "<td>{$field['field_width']}</td>";
    echo "<td>";
    echo "<select name='set_width'>";
    echo "<option value='100'>100% (Full Width)</option>";
    echo "<option value='50'>50% (Half Width)</option>";
    echo "<option value='33'>33% (One Third)</option>";
    echo "<option value='25'>25% (Quarter Width)</option>";
    echo "</select>";
    echo "</td>";
    echo "<td>";
    echo "<input type='hidden' name='field_id' value='{$field['id']}'>";
    echo "<input type='submit' value='Set Width'>";
    echo "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</form>";

echo "<p>Done! <a href='admin.php?page=pfb-form-editor'>Go back to Form Editor</a></p>";
