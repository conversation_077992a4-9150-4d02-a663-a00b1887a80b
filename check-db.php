<?php
/**
 * Check database schema for the plugin.
 *
 * This file contains functions to check the database schema.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Check if the field_width column exists
global $wpdb;
$table_name = $wpdb->prefix . 'pfb_form_fields';

echo "<h1>Database Schema Check</h1>";

// Check if the table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
if (!$table_exists) {
    echo "<p>Table {$table_name} does not exist!</p>";
    die();
}

echo "<p>Table {$table_name} exists.</p>";

// Check if the field_width column exists
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
if (empty($column_exists)) {
    echo "<p>Column 'field_width' does not exist in table {$table_name}.</p>";
    
    // Add the column
    echo "<p>Attempting to add the column...</p>";
    $result = $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN field_width varchar(10) DEFAULT '100' AFTER field_required");
    
    if ($result === false) {
        echo "<p>Failed to add field_width column. Error: " . $wpdb->last_error . "</p>";
    } else {
        echo "<p>Successfully added field_width column.</p>";
    }
} else {
    echo "<p>Column 'field_width' exists in table {$table_name}.</p>";
}

// Show the table structure
echo "<h2>Table Structure</h2>";
$table_structure = $wpdb->get_results("DESCRIBE {$table_name}");
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($table_structure as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "<td>{$column->Extra}</td>";
    echo "</tr>";
}
echo "</table>";

// Show some sample data
echo "<h2>Sample Data</h2>";
$sample_data = $wpdb->get_results("SELECT id, form_id, field_type, field_label, field_name, field_required, field_width, field_order FROM {$table_name} LIMIT 10");
if (!empty($sample_data)) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Form ID</th><th>Type</th><th>Label</th><th>Name</th><th>Required</th><th>Width</th><th>Order</th></tr>";
    foreach ($sample_data as $row) {
        echo "<tr>";
        echo "<td>{$row->id}</td>";
        echo "<td>{$row->form_id}</td>";
        echo "<td>{$row->field_type}</td>";
        echo "<td>{$row->field_label}</td>";
        echo "<td>{$row->field_name}</td>";
        echo "<td>{$row->field_required}</td>";
        echo "<td>{$row->field_width}</td>";
        echo "<td>{$row->field_order}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No data found in the table.</p>";
}

// Force update the database
echo "<h2>Force Database Update</h2>";
require_once 'includes/update-db.php';
$result = pfb_add_field_width_column();
echo "<p>Database update result: " . ($result ? 'Success' : 'Failed') . "</p>";

// Check if the field_width column exists after update
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
echo "<p>Column 'field_width' exists after update: " . (!empty($column_exists) ? 'Yes' : 'No') . "</p>";

echo "<p>Done!</p>";
