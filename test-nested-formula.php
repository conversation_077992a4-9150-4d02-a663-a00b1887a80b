<?php
/**
 * Test script for nested formula calculation
 *
 * This script tests the specific formula pattern that's causing issues.
 */

// Load WordPress
require_once( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/wp-load.php' );

// Check if user is logged in and has admin privileges
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'You do not have sufficient permissions to access this page.' );
}

// Include required files
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-simple-calculator.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-formula-parser.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test the problematic formula
function test_problematic_formula() {
    // The problematic formula pattern
    $formula = '(floor(max(0,{X}-5000)/1000))*{Z}';
    
    // Test values
    $field_values = array(
        'X' => 6000,
        'Y' => 1000,
        'Z' => 2
    );
    
    echo '<h1>Testing Problematic Formula</h1>';
    echo '<p>Formula: ' . htmlspecialchars($formula) . '</p>';
    echo '<p>X = ' . $field_values['X'] . ', Y = ' . $field_values['Y'] . ', Z = ' . $field_values['Z'] . '</p>';
    
    // Start output buffering to capture debug logs
    ob_start();
    
    // Test with Simple Calculator
    $simple_calculator = new PFB_Simple_Calculator(array(), true);
    $simple_result = $simple_calculator->calculate($formula, $field_values);
    
    // Test with Formula Parser
    $formula_parser = new PFB_Formula_Parser(array(), $field_values, true);
    $parser_result = $formula_parser->evaluate($formula);
    
    // Calculate manually for comparison
    $x = $field_values['X'];
    $z = $field_values['Z'];
    $diff = max(0, $x - 5000);
    $divided = $diff / 1000;
    $floored = floor($divided);
    $manual_result = $floored * $z;
    
    // Get debug output
    $debug_output = ob_get_clean();
    
    // Display results
    echo '<h2>Results</h2>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Calculator</th><th>Result</th></tr>';
    echo '<tr><td>Simple Calculator</td><td>' . $simple_result . '</td></tr>';
    echo '<tr><td>Formula Parser</td><td>' . $parser_result . '</td></tr>';
    echo '<tr><td>Manual Calculation</td><td>' . $manual_result . '</td></tr>';
    echo '</table>';
    
    // Display debug output
    echo '<h2>Debug Output</h2>';
    echo '<pre style="background: #f5f5f5; padding: 10px; max-height: 400px; overflow: auto;">';
    echo htmlspecialchars($debug_output);
    echo '</pre>';
    
    // Test variations of the formula
    echo '<h2>Testing Formula Variations</h2>';
    
    $variations = array(
        'Original' => '(floor(max(0,{X}-5000)/1000))*{Z}',
        'Without outer parentheses' => 'floor(max(0,{X}-5000)/1000)*{Z}',
        'With different spacing' => '(floor(max(0, {X} - 5000) / 1000)) * {Z}',
        'With different order' => '{Z}*(floor(max(0,{X}-5000)/1000))',
        'With simplified max' => '(floor({X}/1000))*{Z}',
        'With simplified floor' => 'floor({X}/1000)*{Z}'
    );
    
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Variation</th><th>Formula</th><th>Simple Calculator</th><th>Formula Parser</th></tr>';
    
    foreach ($variations as $name => $var_formula) {
        $simple_result = $simple_calculator->calculate($var_formula, $field_values);
        $parser_result = $formula_parser->evaluate($var_formula);
        
        echo '<tr>';
        echo '<td>' . $name . '</td>';
        echo '<td>' . htmlspecialchars($var_formula) . '</td>';
        echo '<td>' . $simple_result . '</td>';
        echo '<td>' . $parser_result . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
}

// Run the test
test_problematic_formula();
