<?php
/**
 * Check database directly.
 *
 * This script directly checks the database tables and values.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

echo "<h1>Check Database Directly</h1>";

// Get form ID from query string
$form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;

if (!$form_id) {
    die('No form ID provided. Please add ?form_id=X to the URL.');
}

echo "<p>Checking form ID: {$form_id}</p>";

// Check database tables
global $wpdb;
$tables = array(
    'pfb_forms' => $wpdb->prefix . 'pfb_forms',
    'pfb_form_fields' => $wpdb->prefix . 'pfb_form_fields',
    'pfb_variables' => $wpdb->prefix . 'pfb_variables'
);

echo "<h2>Database Tables</h2>";
echo "<table border='1'>";
echo "<tr><th>Table</th><th>Exists</th></tr>";

foreach ($tables as $name => $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td>" . ($exists ? 'Yes' : 'No') . "</td>";
    echo "</tr>";
}

echo "</table>";

// Check form fields table structure
$table_name = $wpdb->prefix . 'pfb_form_fields';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");

if ($table_exists) {
    echo "<h2>Form Fields Table Structure</h2>";
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "<td>{$column->Extra}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Check form fields data
    echo "<h2>Form Fields Data</h2>";
    $fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$table_name} WHERE form_id = %d", $form_id), ARRAY_A);
    
    if (!empty($fields)) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Type</th><th>Label</th><th>Name</th><th>Required</th><th>Width</th><th>Order</th><th>Options</th></tr>";
        
        foreach ($fields as $field) {
            $options = maybe_unserialize($field['field_options']);
            
            echo "<tr>";
            echo "<td>{$field['id']}</td>";
            echo "<td>{$field['field_type']}</td>";
            echo "<td>{$field['field_label']}</td>";
            echo "<td>{$field['field_name']}</td>";
            echo "<td>{$field['field_required']}</td>";
            echo "<td>{$field['field_width']}</td>";
            echo "<td>{$field['field_order']}</td>";
            echo "<td><pre>" . print_r($options, true) . "</pre></td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No fields found for form ID {$form_id}.</p>";
    }
}

// Check form data
$form_table = $wpdb->prefix . 'pfb_forms';
$form_exists = $wpdb->get_var("SHOW TABLES LIKE '{$form_table}'");

if ($form_exists) {
    echo "<h2>Form Data</h2>";
    $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$form_table} WHERE id = %d", $form_id), ARRAY_A);
    
    if ($form) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Title</th><th>Description</th><th>Status</th><th>Created</th><th>Modified</th></tr>";
        
        echo "<tr>";
        echo "<td>{$form['id']}</td>";
        echo "<td>{$form['form_title']}</td>";
        echo "<td>{$form['form_description']}</td>";
        echo "<td>{$form['form_status']}</td>";
        echo "<td>{$form['created_at']}</td>";
        echo "<td>{$form['modified_at']}</td>";
        echo "</tr>";
        
        echo "</table>";
    } else {
        echo "<p>No form found with ID {$form_id}.</p>";
    }
}

echo "<p>Done! <a href='admin.php?page=pfb-form-editor'>Go back to Form Editor</a></p>";
