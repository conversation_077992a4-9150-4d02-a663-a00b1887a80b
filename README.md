# Price Form Builder

A beautiful modern smart dynamic form builder for price calculation with multi-language and multi-currency support.

## Description

Price Form Builder is a powerful WordPress plugin that allows you to create dynamic price calculator forms with a beautiful, modern interface. The plugin supports multiple languages and currencies, making it perfect for international businesses.

### Key Features

- **Dynamic Form Builder**: Create forms with various field types including dropdowns, checkboxes, radio buttons, text inputs, and total fields.
- **Base Price Menu**: Define variables and categories that can be used in your forms for dynamic pricing.
- **Formula Calculation**: Use mathematical formulas to calculate prices based on user input.
- **Multi-language Support**: Translate your forms into multiple languages.
- **Multi-currency Support**: Display prices in different currencies with automatic conversion.
- **Beautiful Design**: Modern, responsive design for both admin and frontend interfaces.

## Installation

1. Upload the `price-form-builder` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'Price Forms' in the admin menu to start creating your forms

## Usage

### Creating a Form

1. Go to 'Price Forms' > 'Add New' in the WordPress admin menu
2. Give your form a title and description
3. Drag and drop fields from the sidebar to build your form
4. Configure each field's settings by clicking on it
5. Save your form

### Adding Price Variables

1. Go to 'Price Forms' > 'Price Variables'
2. Create categories to organize your variables
3. Add variables with fixed prices or range-based pricing
4. Use these variables in your form fields and formulas

### Setting Up Currencies

1. Go to 'Price Forms' > 'Currencies'
2. Add the currencies you want to support
3. Set exchange rates relative to your default currency
4. Configure display options like symbol position and decimal separators

### Displaying Forms

Use the shortcode to display your form on any page or post:

```
[price_form id="1"]
```

Additional shortcode attributes:
- `title="false"` - Hide the form title
- `currency="USD"` - Set the default currency
- `language="fr_FR"` - Set the default language

## Formulas

In the total field, you can use formulas to calculate the final price. Formulas support:

- Basic arithmetic operations: `+`, `-`, `*`, `/`
- Field references: `{field_name}`
- Variable references: `{variable_key}`
- Conditional logic: `if(condition, true_value, false_value)`
- Functions: `min()`, `max()`, `round()`, `ceil()`, `floor()`

Example formula:
```
{quantity} * {base_price} + if({has_rush_fee}, {rush_fee}, 0)
```

## Translations

The plugin supports translating:
- Form titles and descriptions
- Field labels and options
- Success messages and button text

## Customization

You can customize the appearance of your forms through the Settings page:
- Choose from different themes
- Set custom colors
- Change font family
- Adjust border radius
- Enable/disable animations

## Support

For support, please contact <NAME_EMAIL> or visit our website at https://example.com.

## License

This plugin is licensed under the GPL v2 or later.

## Credits

- Developed by WordPress Developer
- Icons by Material Design Icons
- Fonts by Google Fonts
