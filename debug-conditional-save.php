<?php
/**
 * Debug script to test conditional logic saving through WordPress admin
 */

// Include WordPress
require_once('../../../wp-config.php');

// Simulate admin environment
define('WP_ADMIN', true);
$_SERVER['REQUEST_METHOD'] = 'POST';

// Include plugin files
require_once('admin/class-pfb-admin.php');
require_once('includes/class-pfb-db.php');
require_once('includes/class-pfb-form.php');

echo "<h1>Debug Conditional Logic Save</h1>\n";

// Simulate form data that would be sent from the admin
$test_form_data = array(
    'title' => 'Test Conditional Form',
    'description' => 'Testing conditional logic save',
    'status' => 'draft',
    'fields' => array(
        array(
            'type' => 'dropdown',
            'label' => 'Choose Option',
            'name' => 'choice_field',
            'required' => false,
            'width' => '100',
            'options' => array(
                'items' => array(
                    array('label' => 'Yes', 'value' => 'yes'),
                    array('label' => 'No', 'value' => 'no')
                )
            )
        ),
        array(
            'type' => 'text',
            'label' => 'Show When Yes',
            'name' => 'conditional_field',
            'required' => false,
            'width' => '100',
            'options' => array(),
            'conditional_logic' => array(
                'action' => 'show',
                'operator' => 'and',
                'conditions' => array(
                    array(
                        'field' => 'choice_field',
                        'operator' => 'equals',
                        'value' => 'yes'
                    )
                )
            )
        )
    )
);

echo "<h2>Test Data</h2>\n";
echo "<pre>" . print_r($test_form_data, true) . "</pre>\n";

echo "<h2>Saving Form</h2>\n";

// Test the form saving
$form = new PFB_Form($test_form_data);
$form_id = $form->save();

if ($form_id) {
    echo "✅ Form saved successfully with ID: {$form_id}<br>\n";
    
    // Check what was saved in the database
    global $wpdb;
    
    echo "<h3>Form Fields in Database</h3>\n";
    $fields = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM {$wpdb->prefix}pfb_form_fields WHERE form_id = %d", $form_id),
        ARRAY_A
    );
    
    foreach ($fields as $field) {
        echo "<h4>Field: {$field['field_name']}</h4>\n";
        echo "- Type: {$field['field_type']}<br>\n";
        echo "- Label: {$field['field_label']}<br>\n";
        echo "- Conditional Logic: " . ($field['conditional_logic'] ? 'YES' : 'NO') . "<br>\n";
        
        if ($field['conditional_logic']) {
            $logic = maybe_unserialize($field['conditional_logic']);
            echo "<pre>" . print_r($logic, true) . "</pre>\n";
        }
    }
    
    echo "<h3>Conditions in Database</h3>\n";
    $conditions = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM {$wpdb->prefix}pfb_field_conditions WHERE field_id IN (SELECT id FROM {$wpdb->prefix}pfb_form_fields WHERE form_id = %d)", $form_id),
        ARRAY_A
    );
    
    if (!empty($conditions)) {
        echo "Found " . count($conditions) . " conditions:<br>\n";
        foreach ($conditions as $condition) {
            echo "<pre>" . print_r($condition, true) . "</pre>\n";
        }
    } else {
        echo "❌ No conditions found in pfb_field_conditions table<br>\n";
    }
    
    // Test loading the form back
    echo "<h3>Loading Form Back</h3>\n";
    $loaded_form = PFB_DB::get_form($form_id);
    
    if ($loaded_form && isset($loaded_form['fields'])) {
        foreach ($loaded_form['fields'] as $field) {
            if (isset($field['conditional_logic'])) {
                echo "✅ Field '{$field['field_name']}' loaded with conditional logic<br>\n";
            }
            if (isset($field['conditions'])) {
                echo "✅ Field '{$field['field_name']}' loaded with " . count($field['conditions']) . " conditions<br>\n";
            }
        }
    }
    
    // Clean up
    PFB_DB::delete_form($form_id);
    echo "<br>✅ Test form deleted<br>\n";
    
} else {
    echo "❌ Failed to save form<br>\n";
}

echo "<h2>Test Complete</h2>\n";
?>
