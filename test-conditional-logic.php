<?php
/**
 * Test file for conditional logic functionality
 * 
 * This file tests the conditional logic saving and loading functionality
 * to ensure it's working correctly.
 */

// Include WordPress
require_once('../../../wp-config.php');

// Include plugin files
require_once('includes/class-pfb-db.php');
require_once('includes/class-pfb-conditional-logic.php');
require_once('includes/update-db.php');

echo "<h1>Conditional Logic Test</h1>\n";

// Test 1: Check if tables exist
echo "<h2>Test 1: Database Tables</h2>\n";

global $wpdb;

// Check if field conditions table exists
$conditions_table = $wpdb->prefix . 'pfb_field_conditions';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$conditions_table}'");

if ($table_exists == $conditions_table) {
    echo "✅ Field conditions table exists<br>\n";
    
    // Check table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$conditions_table}");
    echo "Table columns:<br>\n";
    foreach ($columns as $column) {
        echo "- {$column->Field} ({$column->Type})<br>\n";
    }
} else {
    echo "❌ Field conditions table does not exist<br>\n";
    echo "Creating table...<br>\n";
    pfb_create_field_conditions_table();
    pfb_update_field_conditions_table();
}

// Test 2: Test conditional logic saving
echo "<h2>Test 2: Conditional Logic Saving</h2>\n";

// Create a test form
$test_form_data = array(
    'title' => 'Test Form for Conditional Logic',
    'description' => 'Testing conditional logic functionality',
    'status' => 'draft',
    'fields' => array(
        array(
            'type' => 'dropdown',
            'label' => 'Select Option',
            'name' => 'select_option',
            'required' => false,
            'width' => '100',
            'options' => array(
                'items' => array(
                    array('label' => 'Option 1', 'value' => 'option1'),
                    array('label' => 'Option 2', 'value' => 'option2')
                )
            )
        ),
        array(
            'type' => 'text',
            'label' => 'Conditional Text Field',
            'name' => 'conditional_text',
            'required' => false,
            'width' => '100',
            'options' => array(),
            'conditional_logic' => array(
                'action' => 'show',
                'operator' => 'and',
                'conditions' => array(
                    array(
                        'field' => 'select_option',
                        'operator' => 'equals',
                        'value' => 'option1'
                    )
                )
            )
        )
    )
);

echo "Saving test form...<br>\n";
$form_id = PFB_DB::save_form($test_form_data);

if ($form_id) {
    echo "✅ Test form saved with ID: {$form_id}<br>\n";
    
    // Test 3: Check if conditions were saved
    echo "<h2>Test 3: Verify Conditions Saved</h2>\n";
    
    $conditions = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM {$conditions_table} WHERE field_id IN (SELECT id FROM {$wpdb->prefix}pfb_form_fields WHERE form_id = %d)", $form_id),
        ARRAY_A
    );
    
    if (!empty($conditions)) {
        echo "✅ Found " . count($conditions) . " conditions saved<br>\n";
        foreach ($conditions as $condition) {
            echo "- Field ID: {$condition['field_id']}, Condition Field: {$condition['condition_field_name']}, Operator: {$condition['condition_operator']}, Value: {$condition['condition_value']}<br>\n";
        }
    } else {
        echo "❌ No conditions found in database<br>\n";
    }
    
    // Test 4: Test loading form with conditions
    echo "<h2>Test 4: Load Form with Conditions</h2>\n";
    
    $loaded_form = PFB_DB::get_form($form_id);
    if ($loaded_form && isset($loaded_form['fields'])) {
        echo "✅ Form loaded successfully<br>\n";
        
        foreach ($loaded_form['fields'] as $field) {
            if (isset($field['conditional_logic'])) {
                echo "✅ Field '{$field['field_name']}' has conditional logic:<br>\n";
                echo "<pre>" . print_r($field['conditional_logic'], true) . "</pre>\n";
            }
            
            if (isset($field['conditions'])) {
                echo "✅ Field '{$field['field_name']}' has " . count($field['conditions']) . " conditions from database<br>\n";
            }
        }
    } else {
        echo "❌ Failed to load form<br>\n";
    }
    
    // Clean up
    echo "<h2>Cleanup</h2>\n";
    PFB_DB::delete_form($form_id);
    echo "✅ Test form deleted<br>\n";
    
} else {
    echo "❌ Failed to save test form<br>\n";
}

echo "<h2>Test Complete</h2>\n";
echo "Check the error log for detailed debugging information.<br>\n";
?>
