<?php
/**
 * Conditional Logic Handler for the plugin.
 *
 * This class handles all conditional logic operations including
 * evaluation, field dependency resolution, and condition management.
 *
 * @since      1.0.2
 */
class PFB_Conditional_Logic {

    /**
     * Available condition operators.
     *
     * @since    1.0.2
     * @access   private
     * @var      array    $operators    Available operators for conditions.
     */
    private static $operators = array(
        'equals'        => 'Equals',
        'not_equals'    => 'Not Equals',
        'greater_than'  => 'Greater Than',
        'less_than'     => 'Less Than',
        'greater_equal' => 'Greater Than or Equal',
        'less_equal'    => 'Less Than or Equal',
        'contains'      => 'Contains',
        'not_contains'  => 'Does Not Contain',
        'starts_with'   => 'Starts With',
        'ends_with'     => 'Ends With',
        'is_empty'      => 'Is Empty',
        'is_not_empty'  => 'Is Not Empty',
        'in_array'      => 'In Array',
        'not_in_array'  => 'Not In Array'
    );

    /**
     * Available condition actions.
     *
     * @since    1.0.2
     * @access   private
     * @var      array    $actions    Available actions for conditions.
     */
    private static $actions = array(
        'show'     => 'Show Field',
        'hide'     => 'Hide Field',
        'enable'   => 'Enable Field',
        'disable'  => 'Disable Field',
        'require'  => 'Make Required',
        'optional' => 'Make Optional'
    );

    /**
     * Get available operators.
     *
     * @since    1.0.2
     * @return   array    Available operators.
     */
    public static function get_operators() {
        return self::$operators;
    }

    /**
     * Get available actions.
     *
     * @since    1.0.2
     * @return   array    Available actions.
     */
    public static function get_actions() {
        return self::$actions;
    }

    /**
     * Evaluate a single condition.
     *
     * @since    1.0.2
     * @param    string   $field_value      Value of the field being checked.
     * @param    string   $operator         Comparison operator.
     * @param    string   $condition_value  Value to compare against.
     * @return   bool                       True if condition is met, false otherwise.
     */
    public static function evaluate_condition($field_value, $operator, $condition_value) {
        // Convert values to appropriate types for comparison
        $field_value = trim($field_value);
        $condition_value = trim($condition_value);

        switch ($operator) {
            case 'equals':
                return $field_value === $condition_value;

            case 'not_equals':
                return $field_value !== $condition_value;

            case 'greater_than':
                return is_numeric($field_value) && is_numeric($condition_value) && 
                       floatval($field_value) > floatval($condition_value);

            case 'less_than':
                return is_numeric($field_value) && is_numeric($condition_value) && 
                       floatval($field_value) < floatval($condition_value);

            case 'greater_equal':
                return is_numeric($field_value) && is_numeric($condition_value) && 
                       floatval($field_value) >= floatval($condition_value);

            case 'less_equal':
                return is_numeric($field_value) && is_numeric($condition_value) && 
                       floatval($field_value) <= floatval($condition_value);

            case 'contains':
                return stripos($field_value, $condition_value) !== false;

            case 'not_contains':
                return stripos($field_value, $condition_value) === false;

            case 'starts_with':
                return stripos($field_value, $condition_value) === 0;

            case 'ends_with':
                return substr_compare($field_value, $condition_value, -strlen($condition_value), strlen($condition_value), true) === 0;

            case 'is_empty':
                return empty($field_value);

            case 'is_not_empty':
                return !empty($field_value);

            case 'in_array':
                $array_values = array_map('trim', explode(',', $condition_value));
                return in_array($field_value, $array_values);

            case 'not_in_array':
                $array_values = array_map('trim', explode(',', $condition_value));
                return !in_array($field_value, $array_values);

            default:
                return false;
        }
    }

    /**
     * Evaluate multiple conditions for a field.
     *
     * @since    1.0.2
     * @param    array    $conditions       Array of conditions to evaluate.
     * @param    array    $form_data        Form data with field values.
     * @param    string   $logical_operator Logical operator (and/or).
     * @return   bool                       True if all conditions are met, false otherwise.
     */
    public static function evaluate_conditions($conditions, $form_data, $logical_operator = 'and') {
        if (empty($conditions)) {
            return true; // No conditions means always show
        }

        $results = array();

        foreach ($conditions as $condition) {
            $field_name = isset($condition['field']) ? $condition['field'] : '';
            $operator = isset($condition['operator']) ? $condition['operator'] : 'equals';
            $value = isset($condition['value']) ? $condition['value'] : '';

            // Get the field value from form data
            $field_value = isset($form_data[$field_name]) ? $form_data[$field_name] : '';

            // Handle array values (for checkboxes)
            if (is_array($field_value)) {
                $field_value = implode(',', $field_value);
            }

            $result = self::evaluate_condition($field_value, $operator, $value);
            $results[] = $result;

            error_log("PFB Conditional Logic: Field '{$field_name}' = '{$field_value}' {$operator} '{$value}' = " . ($result ? 'true' : 'false'));
        }

        // Apply logical operator
        if ($logical_operator === 'or') {
            return in_array(true, $results); // At least one condition must be true
        } else {
            return !in_array(false, $results); // All conditions must be true
        }
    }

    /**
     * Get field conditions from database.
     *
     * @since    1.0.2
     * @param    int      $field_id    Field ID.
     * @return   array                 Array of conditions.
     */
    public static function get_field_conditions($field_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_field_conditions';

        $conditions = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE field_id = %d ORDER BY condition_group, id",
                $field_id
            ),
            ARRAY_A
        );

        return $conditions ? $conditions : array();
    }

    /**
     * Save field conditions to database.
     *
     * @since    1.0.2
     * @param    int      $field_id     Field ID.
     * @param    array    $conditions   Array of conditions.
     * @return   bool                   True on success, false on failure.
     */
    public static function save_field_conditions($field_id, $conditions) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_field_conditions';

        // Delete existing conditions for this field
        $wpdb->delete($table_name, array('field_id' => $field_id));

        if (empty($conditions)) {
            return true; // No conditions to save
        }

        // Insert new conditions
        foreach ($conditions as $condition) {
            $condition_data = array(
                'field_id'           => $field_id,
                'condition_field_id' => isset($condition['field_id']) ? intval($condition['field_id']) : 0,
                'condition_operator' => isset($condition['operator']) ? sanitize_text_field($condition['operator']) : 'equals',
                'condition_value'    => isset($condition['value']) ? sanitize_text_field($condition['value']) : '',
                'condition_action'   => isset($condition['action']) ? sanitize_text_field($condition['action']) : 'show',
                'condition_group'    => isset($condition['group']) ? intval($condition['group']) : 1,
                'logical_operator'   => isset($condition['logical_operator']) ? sanitize_text_field($condition['logical_operator']) : 'and'
            );

            $result = $wpdb->insert($table_name, $condition_data);

            if ($result === false) {
                error_log('PFB: Failed to save condition for field ' . $field_id . ': ' . $wpdb->last_error);
                return false;
            }
        }

        return true;
    }

    /**
     * Get all conditional logic rules for a form.
     *
     * @since    1.0.2
     * @param    int      $form_id    Form ID.
     * @return   array                Array of conditional logic rules.
     */
    public static function get_form_conditional_logic($form_id) {
        global $wpdb;

        $fields_table = $wpdb->prefix . 'pfb_form_fields';
        $conditions_table = $wpdb->prefix . 'pfb_field_conditions';

        // Get all fields with conditional logic for this form
        $sql = "
            SELECT 
                f.id as field_id,
                f.field_name,
                f.conditional_logic,
                f.conditional_action,
                f.conditional_operator,
                c.condition_field_id,
                c.condition_operator,
                c.condition_value,
                c.condition_action as condition_action,
                c.condition_group,
                c.logical_operator
            FROM {$fields_table} f
            LEFT JOIN {$conditions_table} c ON f.id = c.field_id
            WHERE f.form_id = %d
            AND (f.conditional_logic IS NOT NULL OR c.id IS NOT NULL)
            ORDER BY f.field_order, c.condition_group, c.id
        ";

        $results = $wpdb->get_results(
            $wpdb->prepare($sql, $form_id),
            ARRAY_A
        );

        // Group conditions by field
        $conditional_logic = array();

        foreach ($results as $row) {
            $field_id = $row['field_id'];
            $field_name = $row['field_name'];

            if (!isset($conditional_logic[$field_name])) {
                $conditional_logic[$field_name] = array(
                    'field_id' => $field_id,
                    'action' => $row['conditional_action'] ?: 'show',
                    'operator' => $row['conditional_operator'] ?: 'and',
                    'conditions' => array()
                );
            }

            // Add condition if it exists
            if ($row['condition_field_id']) {
                $conditional_logic[$field_name]['conditions'][] = array(
                    'field_id' => $row['condition_field_id'],
                    'operator' => $row['condition_operator'],
                    'value' => $row['condition_value'],
                    'group' => $row['condition_group'],
                    'logical_operator' => $row['logical_operator']
                );
            }
        }

        return $conditional_logic;
    }
}
