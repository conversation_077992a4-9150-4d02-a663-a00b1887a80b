<?php
/**
 * Database operations for the plugin.
 *
 * @since      1.0.0
 */
class PFB_DB {

    /**
     * Get forms from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of forms.
     */
    public static function get_forms($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'DESC',
            'status'     => 'publish'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_forms';

        $sql = "SELECT * FROM $table_name";

        if (!empty($args['status'])) {
            $sql .= $wpdb->prepare(" WHERE status = %s", $args['status']);
        }

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $forms = $wpdb->get_results($sql, ARRAY_A);

        return $forms;
    }

    /**
     * Get a single form from the database.
     *
     * @since    1.0.0
     * @param    int      $id      Form ID.
     * @return   array             Form data.
     */
    public static function get_form($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        $form = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id),
            ARRAY_A
        );

        if (!$form) {
            return null;
        }

        // Get form fields
        $form['fields'] = self::get_form_fields($id);

        return $form;
    }

    /**
     * Get form fields from the database.
     *
     * @since    1.0.0
     * @param    int      $form_id    Form ID.
     * @return   array                Array of form fields.
     */
    public static function get_form_fields($form_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_form_fields';

        $fields = $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $table_name WHERE form_id = %d ORDER BY field_order ASC", $form_id),
            ARRAY_A
        );



        // Check if field_width column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");

        foreach ($fields as $key => $field) {
            // Ensure field_width is set
            if (!isset($field['field_width']) || empty($field['field_width'])) {
                // Default to 100% if not set
                $fields[$key]['field_width'] = '100';
            }

            if (!empty($field['field_options'])) {
                // Try to unserialize the field options
                $fields[$key]['field_options'] = maybe_unserialize($field['field_options']);

                // Log the unserialized field options
                error_log('PFB: Field options after unserialization: ' . print_r($fields[$key]['field_options'], true));

                // Debug: Log the unserialized field options
                error_log('PFB: Unserialized field options for field ' . $field['id'] . ' (' . $field['field_type'] . '): ' . print_r($fields[$key]['field_options'], true));

                // Check if width is in field_options and field_width is not set
                if (isset($fields[$key]['field_options']['width']) && $fields[$key]['field_width'] === '100') {
                    $fields[$key]['field_width'] = $fields[$key]['field_options']['width'];
                    error_log('PFB: Using width from field_options for field ' . $field['id'] . ': ' . $fields[$key]['field_width']);

                    // Remove width from options to avoid duplication
                    unset($fields[$key]['field_options']['width']);
                }

                // For subtotal fields, ensure the lines array exists
                if ($field['field_type'] === 'subtotal') {
                    if (!isset($fields[$key]['field_options']['lines']) || !is_array($fields[$key]['field_options']['lines'])) {
                        $fields[$key]['field_options']['lines'] = array(
                            array(
                                'label' => 'Line 1',
                                'formula' => ''
                            )
                        );
                        error_log('PFB: Added default lines to subtotal field options');
                    }

                    // Make sure empty_value is set
                    if (!isset($fields[$key]['field_options']['empty_value'])) {
                        $fields[$key]['field_options']['empty_value'] = '---';
                    }
                }
            }

            // Handle conditional logic
            if (!empty($field['conditional_logic'])) {
                $fields[$key]['conditional_logic'] = maybe_unserialize($field['conditional_logic']);

                // Get conditions from the conditions table
                $fields[$key]['conditions'] = PFB_Conditional_Logic::get_field_conditions($field['id']);

                error_log('PFB: Loaded conditional logic for field ' . $field['id'] . ': ' . print_r($fields[$key]['conditional_logic'], true));
            }
        }

        return $fields;
    }

    /**
     * Save a form to the database.
     *
     * @since    1.0.0
     * @param    array    $form_data    Form data.
     * @return   int                     Form ID.
     */
    public static function save_form($form_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        $form = array(
            'title'       => sanitize_text_field($form_data['title']),
            'description' => sanitize_textarea_field($form_data['description']),
            'status'      => sanitize_text_field($form_data['status'])
        );

        if (isset($form_data['id']) && !empty($form_data['id'])) {
            // Update existing form
            $wpdb->update(
                $table_name,
                $form,
                array('id' => $form_data['id'])
            );

            $form_id = $form_data['id'];
        } else {
            // Insert new form
            $wpdb->insert($table_name, $form);
            $form_id = $wpdb->insert_id;
        }

        // Save form fields
        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            self::save_form_fields($form_id, $form_data['fields']);
        }

        return $form_id;
    }

    /**
     * Save form fields to the database.
     *
     * @since    1.0.0
     * @param    int      $form_id    Form ID.
     * @param    array    $fields     Form fields.
     */
    public static function save_form_fields($form_id, $fields) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_form_fields';

        // Debug: Log the fields being saved
        error_log('PFB: Saving fields for form ' . $form_id . ': ' . print_r($fields, true));

        // Debug: Check if field_width column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
        error_log('PFB: field_width column exists: ' . (empty($column_exists) ? 'NO' : 'YES'));

        // Debug: Check if conditional logic columns exist
        $conditional_logic_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_logic'");
        $conditional_action_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_action'");
        $conditional_operator_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_operator'");

        error_log('PFB: conditional_logic column exists: ' . (empty($conditional_logic_exists) ? 'NO' : 'YES'));
        error_log('PFB: conditional_action column exists: ' . (empty($conditional_action_exists) ? 'NO' : 'YES'));
        error_log('PFB: conditional_operator column exists: ' . (empty($conditional_operator_exists) ? 'NO' : 'YES'));

        // If conditional logic columns don't exist, create them
        if (empty($conditional_logic_exists)) {
            error_log('PFB: Creating conditional logic columns...');
            require_once(plugin_dir_path(__FILE__) . 'update-db.php');
            pfb_add_conditional_logic_columns();
            pfb_create_field_conditions_table();
        }

        // Delete existing fields
        $wpdb->delete($table_name, array('form_id' => $form_id));

        // Insert new fields
        foreach ($fields as $order => $field) {
            // Debug: Log each field's options before serialization
            if (isset($field['options']) && !empty($field['options'])) {
                error_log('Field options before serialization for field type ' . $field['type'] . ': ' . print_r($field['options'], true));
            }

            // If this is a text field and it's hidden, add that to the options
            if ($field['type'] === 'text' && isset($field['hidden']) && $field['hidden']) {
                if (!isset($field['options'])) {
                    $field['options'] = array();
                }
                $field['options']['is_hidden'] = true;

                // Debug log
                error_log('Setting hidden field option for ' . $field['name'] . ': ' . print_r($field['options'], true));
            }

            // Make sure options is an array
            if (!isset($field['options'])) {
                $field['options'] = array();
            }

            // Store width separately (will be saved in field_width column)
            $field_width = isset($field['width']) ? $field['width'] : '100';

            // Make sure the field width is properly formatted (remove % if present)
            $field_width = str_replace('%', '', $field_width);

            // Ensure the field width is a valid numeric value
            if (!is_numeric($field_width)) {
                $field_width = '100';
            }

            // For subtotal fields, make sure lines is an array
            if ($field['type'] === 'subtotal') {
                if (isset($field['options']['lines']) && is_array($field['options']['lines'])) {
                    error_log('Subtotal field lines before serialization: ' . print_r($field['options']['lines'], true));
                } else {
                    // If lines is not set or not an array, initialize it
                    $field['options']['lines'] = array(
                        array(
                            'label' => 'Line 1',
                            'formula' => ''
                        )
                    );
                    error_log('Created default lines for subtotal field');
                }

                // Make sure empty_value is set
                if (!isset($field['options']['empty_value'])) {
                    $field['options']['empty_value'] = '---';
                }
            }

            // Prepare field options for serialization
            $options_to_save = $field['options'];

            // For subtotal fields, ensure the lines array is properly formatted
            if ($field['type'] === 'subtotal' && isset($options_to_save['lines'])) {
                // Filter out empty lines and make sure each line has a label and formula
                $filtered_lines = array();
                foreach ($options_to_save['lines'] as $line) {
                    // Only keep lines that have either a label or a formula
                    if ((isset($line['label']) && !empty($line['label'])) ||
                        (isset($line['formula']) && !empty($line['formula']))) {

                        // Make sure label and formula are set
                        if (!isset($line['label']) || empty($line['label'])) {
                            $line['label'] = 'Line';
                        }
                        if (!isset($line['formula'])) {
                            $line['formula'] = '';
                        }

                        $filtered_lines[] = $line;
                    }
                }

                // Make sure we have at least one line
                if (empty($filtered_lines)) {
                    $filtered_lines[] = array(
                        'label' => 'Line 1',
                        'formula' => ''
                    );
                }

                // Replace the lines with the filtered ones
                $options_to_save['lines'] = $filtered_lines;

                // Make sure empty_value is set
                if (!isset($options_to_save['empty_value'])) {
                    $options_to_save['empty_value'] = '---';
                }

                error_log('Subtotal field options before serialization: ' . print_r($options_to_save, true));
            }

            // Serialize options using WordPress serialization
            $serialized_options = maybe_serialize($options_to_save);

            // Log the serialized options for debugging
            error_log('Options to save: ' . print_r($options_to_save, true));
            error_log('Serialized options: ' . $serialized_options);

            // Handle conditional logic - CLEAN NEW IMPLEMENTATION
            $conditional_logic = null;

            if (isset($field['conditional_logic']) && !empty($field['conditional_logic']) && $field['conditional_logic'] !== null) {
                $conditional_logic = maybe_serialize($field['conditional_logic']);
                error_log('PFB: Saving conditional logic for field ' . $field['name'] . ': ' . $conditional_logic);
            } else {
                error_log('PFB: No conditional logic for field: ' . $field['name']);
            }

            $field_data = array(
                'form_id'               => $form_id,
                'field_type'            => sanitize_text_field($field['type']),
                'field_label'           => sanitize_text_field($field['label']),
                'field_name'            => sanitize_key($field['name']),
                'field_required'        => isset($field['required']) ? 1 : 0,
                'field_width'           => $field_width,
                'field_order'           => $order,
                'field_options'         => $serialized_options,
                'conditional_logic'     => $conditional_logic
            );

            $result = $wpdb->insert($table_name, $field_data);

            if ($result !== false) {
                $field_id = $wpdb->insert_id;
                error_log('PFB: Successfully saved field ' . $field['name'] . ' with ID: ' . $field_id);
            } else {
                error_log('PFB: Failed to save field ' . $field['name'] . ': ' . $wpdb->last_error);
            }
        }
    }

    /**
     * Delete a form from the database.
     *
     * @since    1.0.0
     * @param    int      $id    Form ID.
     * @return   bool            True on success, false on failure.
     */
    public static function delete_form($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        // Delete form fields first
        $fields_table = $wpdb->prefix . 'pfb_form_fields';
        $wpdb->delete($fields_table, array('form_id' => $id));

        // Delete form
        $result = $wpdb->delete($table_name, array('id' => $id));

        return $result !== false;
    }

    /**
     * Get price categories from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of price categories.
     */
    public static function get_price_categories($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'DESC'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_price_categories';

        $sql = "SELECT * FROM $table_name";

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $categories = $wpdb->get_results($sql, ARRAY_A);

        return $categories;
    }

    /**
     * Get price variables from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of price variables.
     */
    public static function get_price_variables($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'      => 20,
            'offset'      => 0,
            'orderby'     => 'id',
            'order'       => 'DESC',
            'category_id' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_price_variables';

        $sql = "SELECT * FROM $table_name";

        if (!empty($args['category_id'])) {
            $sql .= $wpdb->prepare(" WHERE category_id = %d", $args['category_id']);
        }

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $variables = $wpdb->get_results($sql, ARRAY_A);

        foreach ($variables as &$variable) {
            if (!empty($variable['price_ranges'])) {
                $variable['price_ranges'] = maybe_unserialize($variable['price_ranges']);
            }
        }

        return $variables;
    }

    /**
     * Save a price category to the database.
     *
     * @since    1.0.0
     * @param    array    $category_data    Category data.
     * @return   int                         Category ID.
     */
    public static function save_price_category($category_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_price_categories';

        $category = array(
            'name'        => sanitize_text_field($category_data['name']),
            'description' => sanitize_textarea_field($category_data['description'])
        );

        if (isset($category_data['id']) && !empty($category_data['id'])) {
            // Update existing category
            $wpdb->update(
                $table_name,
                $category,
                array('id' => $category_data['id'])
            );

            $category_id = $category_data['id'];
        } else {
            // Insert new category
            $wpdb->insert($table_name, $category);
            $category_id = $wpdb->insert_id;
        }

        return $category_id;
    }

    /**
     * Save a price variable to the database.
     *
     * @since    1.0.0
     * @param    array    $variable_data    Variable data.
     * @return   int                         Variable ID.
     */
    public static function save_price_variable($variable_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_price_variables';

        $variable = array(
            'category_id' => intval($variable_data['category_id']),
            'name'        => sanitize_text_field($variable_data['name']),
            'variable_key' => sanitize_key($variable_data['variable_key']),
            'price_type'  => sanitize_text_field($variable_data['price_type']),
            'price_value' => floatval($variable_data['price_value'])
        );

        if (isset($variable_data['price_ranges']) && is_array($variable_data['price_ranges'])) {
            $variable['price_ranges'] = maybe_serialize($variable_data['price_ranges']);
        }

        if (isset($variable_data['id']) && !empty($variable_data['id'])) {
            // Update existing variable
            $wpdb->update(
                $table_name,
                $variable,
                array('id' => $variable_data['id'])
            );

            $variable_id = $variable_data['id'];
        } else {
            // Insert new variable
            $wpdb->insert($table_name, $variable);
            $variable_id = $wpdb->insert_id;
        }

        return $variable_id;
    }

    /**
     * Get currencies from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of currencies.
     */
    public static function get_currencies($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'ASC'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $sql = "SELECT * FROM $table_name";

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $currencies = $wpdb->get_results($sql, ARRAY_A);

        return $currencies;
    }

    /**
     * Get the default currency.
     *
     * @since    1.0.0
     * @return   array    Default currency.
     */
    public static function get_default_currency() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = $wpdb->get_row(
            "SELECT * FROM $table_name WHERE is_default = 1",
            ARRAY_A
        );

        return $currency;
    }

    /**
     * Save a currency to the database.
     *
     * @since    1.0.0
     * @param    array    $currency_data    Currency data.
     * @return   int                         Currency ID.
     */
    public static function save_currency($currency_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = array(
            'name'               => sanitize_text_field($currency_data['name']),
            'code'               => sanitize_text_field($currency_data['code']),
            'symbol'             => sanitize_text_field($currency_data['symbol']),
            'exchange_rate'      => floatval($currency_data['exchange_rate']),
            'is_default'         => isset($currency_data['is_default']) ? 1 : 0,
            'symbol_position'    => sanitize_text_field($currency_data['symbol_position']),
            'rtl_symbol_position'=> isset($currency_data['rtl_symbol_position']) ? sanitize_text_field($currency_data['rtl_symbol_position']) : null,
            'thousand_separator' => sanitize_text_field($currency_data['thousand_separator']),
            'decimal_separator'  => sanitize_text_field($currency_data['decimal_separator']),
            'decimal_places'     => intval($currency_data['decimal_places'])
        );

        // If this is the default currency, unset all other default currencies
        if ($currency['is_default']) {
            $wpdb->update(
                $table_name,
                array('is_default' => 0),
                array('is_default' => 1)
            );
        }

        if (isset($currency_data['id']) && !empty($currency_data['id'])) {
            // Update existing currency
            $wpdb->update(
                $table_name,
                $currency,
                array('id' => $currency_data['id'])
            );

            $currency_id = $currency_data['id'];
        } else {
            // Insert new currency
            $wpdb->insert($table_name, $currency);
            $currency_id = $wpdb->insert_id;
        }

        return $currency_id;
    }

    /**
     * Get translations from the database.
     *
     * @since    1.0.0
     * @param    string   $object_type     Object type.
     * @param    int      $object_id       Object ID.
     * @param    string   $language_code   Language code.
     * @return   array                     Array of translations.
     */
    public static function get_translations($object_type, $object_id, $language_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_translations';

        $translations = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE object_type = %s AND object_id = %d AND language_code = %s",
                $object_type,
                $object_id,
                $language_code
            ),
            ARRAY_A
        );

        $result = array();

        foreach ($translations as $translation) {
            $result[$translation['field_name']] = $translation['translated_value'];
        }

        return $result;
    }

    /**
     * Save translations to the database.
     *
     * @since    1.0.0
     * @param    string   $object_type     Object type.
     * @param    int      $object_id       Object ID.
     * @param    string   $language_code   Language code.
     * @param    array    $translations    Translations.
     */
    public static function save_translations($object_type, $object_id, $language_code, $translations) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_translations';

        // Delete existing translations
        $wpdb->delete(
            $table_name,
            array(
                'object_type'   => $object_type,
                'object_id'     => $object_id,
                'language_code' => $language_code
            )
        );

        // Insert new translations
        foreach ($translations as $field_name => $translated_value) {
            $wpdb->insert(
                $table_name,
                array(
                    'object_type'      => $object_type,
                    'object_id'        => $object_id,
                    'language_code'    => $language_code,
                    'field_name'       => $field_name,
                    'translated_value' => $translated_value
                )
            );
        }
    }
}
