<?php
/**
 * Update database schema for the plugin.
 *
 * This file contains functions to update the database schema when needed.
 *
 * @since      1.0.0
 */

/**
 * Add conditional logic columns to pfb_form_fields table if they don't exist.
 *
 * @since    1.0.1
 * @return   bool    True on success, false on failure.
 */
function pfb_add_conditional_logic_columns() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_form_fields';

    error_log('PFB: Adding conditional logic columns to table: ' . $table_name);

    $columns_to_add = array(
        'conditional_logic' => "longtext DEFAULT NULL COMMENT 'JSON data for conditional logic rules'",
        'conditional_action' => "varchar(20) DEFAULT 'show' COMMENT 'Action to take when conditions are met: show, hide, enable, disable, require, optional'",
        'conditional_operator' => "varchar(10) DEFAULT 'and' COMMENT 'Logical operator for multiple conditions: and, or'"
    );

    $success = true;

    foreach ($columns_to_add as $column_name => $column_definition) {
        // Check if the column already exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE '{$column_name}'");

        if (empty($column_exists)) {
            error_log('PFB: Adding column: ' . $column_name);

            $sql = "ALTER TABLE {$table_name} ADD COLUMN {$column_name} {$column_definition}";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('PFB: Failed to add ' . $column_name . ' column. Error: ' . $wpdb->last_error);
                $success = false;
            } else {
                error_log('PFB: Successfully added ' . $column_name . ' column');
            }
        } else {
            error_log('PFB: Column ' . $column_name . ' already exists');
        }
    }

    return $success;
}

/**
 * Create field conditions table for complex conditional relationships.
 *
 * @since    1.0.1
 * @return   bool    True on success, false on failure.
 */
function pfb_create_field_conditions_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_field_conditions';
    $charset_collate = $wpdb->get_charset_collate();

    // Check if table already exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");

    if ($table_exists != $table_name) {
        error_log('PFB: Creating field conditions table: ' . $table_name);

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            field_id mediumint(9) NOT NULL COMMENT 'ID of the field that has conditions',
            condition_field_id mediumint(9) NOT NULL COMMENT 'ID of the field being checked',
            condition_operator varchar(20) NOT NULL DEFAULT 'equals' COMMENT 'Comparison operator: equals, not_equals, greater_than, less_than, contains, not_contains, is_empty, is_not_empty',
            condition_value text DEFAULT NULL COMMENT 'Value to compare against',
            condition_action varchar(20) NOT NULL DEFAULT 'show' COMMENT 'Action to take: show, hide, enable, disable, require, optional',
            condition_group int(11) DEFAULT 1 COMMENT 'Group number for complex conditions',
            logical_operator varchar(10) DEFAULT 'and' COMMENT 'Logical operator within group: and, or',
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id),
            KEY field_id (field_id),
            KEY condition_field_id (condition_field_id),
            KEY condition_group (condition_group)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        if ($wpdb->last_error) {
            error_log('PFB: Failed to create field conditions table. Error: ' . $wpdb->last_error);
            return false;
        } else {
            error_log('PFB: Successfully created field conditions table');
            return true;
        }
    } else {
        error_log('PFB: Field conditions table already exists');
        return true;
    }
}

/**
 * Add field_width column to pfb_form_fields table if it doesn't exist.
 *
 * @since    1.0.0
 * @return   bool    True on success, false on failure.
 */
function pfb_add_field_width_column() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_form_fields';

    error_log('PFB: Checking if field_width column exists in table: ' . $table_name);

    // Check if the column already exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");

    error_log('PFB: Column exists check result: ' . print_r($column_exists, true));

    if (empty($column_exists)) {
        error_log('PFB: field_width column does not exist, adding it now');

        // Add the column
        $sql = "ALTER TABLE {$table_name} ADD COLUMN field_width varchar(10) DEFAULT '100' AFTER field_required";
        error_log('PFB: Running SQL: ' . $sql);

        $result = $wpdb->query($sql);

        if ($result === false) {
            error_log('PFB: Failed to add field_width column to pfb_form_fields table. Error: ' . $wpdb->last_error);
            return false;
        }

        error_log('PFB: Successfully added field_width column');

        // Update existing fields to set width from field_options
        $fields = $wpdb->get_results("SELECT id, field_options FROM {$table_name}", ARRAY_A);

        error_log('PFB: Found ' . count($fields) . ' fields to update');

        foreach ($fields as $field) {
            $options = maybe_unserialize($field['field_options']);

            error_log('PFB: Processing field ID ' . $field['id'] . ' with options: ' . print_r($options, true));

            if (is_array($options) && isset($options['width'])) {
                $width = $options['width'];
                error_log('PFB: Found width ' . $width . ' in options for field ID ' . $field['id']);

                $update_result = $wpdb->update(
                    $table_name,
                    array('field_width' => $width),
                    array('id' => $field['id'])
                );

                error_log('PFB: Updated field_width for field ID ' . $field['id'] . ': ' . ($update_result !== false ? 'success' : 'failed - ' . $wpdb->last_error));

                // Remove width from options to avoid duplication
                unset($options['width']);
                $serialized_options = maybe_serialize($options);

                $update_result = $wpdb->update(
                    $table_name,
                    array('field_options' => $serialized_options),
                    array('id' => $field['id'])
                );

                error_log('PFB: Removed width from options for field ID ' . $field['id'] . ': ' . ($update_result !== false ? 'success' : 'failed - ' . $wpdb->last_error));
            } else {
                error_log('PFB: No width found in options for field ID ' . $field['id']);
            }
        }

        error_log('PFB: Successfully added field_width column to pfb_form_fields table and updated existing fields');
        return true;
    }

    // Column already exists
    error_log('PFB: field_width column already exists');
    return true;
}
