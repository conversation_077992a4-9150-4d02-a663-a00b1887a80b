<?php
/**
 * Formula parser for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */
class PFB_Formula_Parser {

    /**
     * Variables for calculation.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $variables    Variables for calculation.
     */
    private $variables = array();

    /**
     * Field values from the form.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $field_values    Field values from the form.
     */
    private $field_values = array();

    /**
     * Debug mode.
     *
     * @since    1.0.0
     * @access   private
     * @var      boolean    $debug    Whether to output debug information.
     */
    private $debug = true;

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     * @param    array    $variables     Price variables.
     * @param    array    $field_values  Field values from the form.
     * @param    boolean  $debug         Whether to output debug information.
     */
    public function __construct($variables = array(), $field_values = array(), $debug = true) {
        $this->variables = $variables;
        $this->field_values = $field_values;
        $this->debug = $debug;
    }

    /**
     * Log debug information.
     *
     * @since    1.0.0
     * @param    string    $message    Debug message.
     */
    private function log($message) {
        if ($this->debug) {
            error_log('[PFB Formula Parser] ' . $message);
        }
    }

    /**
     * Parse and evaluate a formula.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula to evaluate.
     * @return   float                 Result.
     */
    public function evaluate($formula) {
        $this->log('Original formula: ' . $formula);

        // Replace field and variable references with their values
        $formula = $this->replace_references($formula);
        $this->log('Formula after replacing references: ' . $formula);

        // Tokenize the formula
        $tokens = $this->tokenize($formula);
        $this->log('Tokens: ' . print_r($tokens, true));

        // Convert to postfix notation (Reverse Polish Notation)
        $postfix = $this->to_postfix($tokens);
        $this->log('Postfix: ' . print_r($postfix, true));

        // Evaluate the postfix expression
        $result = $this->evaluate_postfix($postfix);
        $this->log('Result: ' . $result);

        return $result;
    }

    /**
     * Replace field and variable references with their values.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula with references.
     * @return   string                Formula with values.
     */
    private function replace_references($formula) {
        // Replace field references
        preg_match_all('/\{([^{}]+)\}/', $formula, $matches);

        foreach ($matches[0] as $index => $match) {
            $field_name = $matches[1][$index];

            if (isset($this->field_values[$field_name])) {
                $value = floatval($this->field_values[$field_name]);
                $formula = str_replace($match, $value, $formula);
                $this->log("Replaced field reference {$match} with {$value}");
            } elseif (isset($this->variables[$field_name])) {
                $value = floatval($this->variables[$field_name]);
                $formula = str_replace($match, $value, $formula);
                $this->log("Replaced variable reference {$match} with {$value}");
            } else {
                $this->log("Warning: Undefined reference {$match}, replacing with 0");
                $formula = str_replace($match, '0', $formula);
            }
        }

        return $formula;
    }

    /**
     * Tokenize a formula into an array of tokens.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula to tokenize.
     * @return   array                 Array of tokens.
     */
    private function tokenize($formula) {
        $tokens = array();
        $current_number = '';

        // Add spaces around operators and parentheses for easier tokenization
        $formula = preg_replace('/([+\-*\/\(\)])/', ' $1 ', $formula);
        $formula = trim(preg_replace('/\s+/', ' ', $formula));

        // Special handling for function names to ensure they're properly recognized
        $formula = preg_replace('/(ceil|floor|round|min|max)\s*\(/', '$1 ( ', $formula);

        $parts = explode(' ', $formula);

        foreach ($parts as $part) {
            $part = trim($part);
            if ($part === '') continue;

            if (is_numeric($part) || (substr($part, 0, 1) === '.' && is_numeric(substr($part, 1)))) {
                // Number
                $tokens[] = array('type' => 'number', 'value' => floatval($part));
            } elseif (in_array($part, array('+', '-', '*', '/'))) {
                // Operator
                $tokens[] = array('type' => 'operator', 'value' => $part);
            } elseif ($part === '(' || $part === ')') {
                // Parenthesis
                $tokens[] = array('type' => 'parenthesis', 'value' => $part);
            } elseif (preg_match('/^(ceil|floor|round|min|max)$/i', $part)) {
                // Function
                $tokens[] = array('type' => 'function', 'value' => strtolower($part));
            } else {
                // Unknown token, try to handle it
                $this->log("Warning: Unknown token '{$part}', ignoring");
            }
        }

        $this->log("Tokenized formula: " . print_r($tokens, true));
        return $tokens;
    }

    /**
     * Convert an infix expression to postfix notation (Reverse Polish Notation).
     *
     * @since    1.0.0
     * @param    array    $tokens    Array of tokens in infix notation.
     * @return   array               Array of tokens in postfix notation.
     */
    private function to_postfix($tokens) {
        $output = array();
        $stack = array();
        $function_stack = array();

        $precedence = array(
            '+' => 1,
            '-' => 1,
            '*' => 2,
            '/' => 2
        );

        foreach ($tokens as $token) {
            if ($token['type'] === 'number') {
                // Numbers go straight to the output
                $output[] = $token;
            } elseif ($token['type'] === 'function') {
                // Functions go on the stack
                $stack[] = $token;
                $function_stack[] = count($output);
            } elseif ($token['type'] === 'operator') {
                // Operators need to consider precedence
                while (!empty($stack) &&
                       end($stack)['type'] === 'operator' &&
                       $precedence[end($stack)['value']] >= $precedence[$token['value']]) {
                    $output[] = array_pop($stack);
                }
                $stack[] = $token;
            } elseif ($token['type'] === 'parenthesis' && $token['value'] === '(') {
                // Opening parenthesis goes on the stack
                $stack[] = $token;
            } elseif ($token['type'] === 'parenthesis' && $token['value'] === ')') {
                // Closing parenthesis pops operators until matching opening parenthesis
                $found_opening = false;

                while (!empty($stack)) {
                    $top = array_pop($stack);

                    if ($top['type'] === 'parenthesis' && $top['value'] === '(') {
                        $found_opening = true;
                        break;
                    } else {
                        $output[] = $top;
                    }
                }

                if (!$found_opening) {
                    $this->log("Warning: Unmatched closing parenthesis");
                }

                // If there's a function at the top of the stack, pop it
                if (!empty($stack) && end($stack)['type'] === 'function') {
                    $func = array_pop($stack);
                    $func_start = array_pop($function_stack);
                    $arg_count = count($output) - $func_start;

                    // Add the function and argument count to the output
                    $output[] = array(
                        'type' => 'function_call',
                        'value' => $func['value'],
                        'arg_count' => $arg_count
                    );
                }
            }
        }

        // Pop any remaining operators from the stack to the output
        while (!empty($stack)) {
            $top = array_pop($stack);

            if ($top['type'] === 'parenthesis') {
                $this->log("Warning: Unmatched opening parenthesis");
            } else {
                $output[] = $top;
            }
        }

        return $output;
    }

    /**
     * Evaluate a postfix expression.
     *
     * @since    1.0.0
     * @param    array    $postfix    Array of tokens in postfix notation.
     * @return   float                Result of the evaluation.
     */
    private function evaluate_postfix($postfix) {
        $stack = array();

        foreach ($postfix as $token) {
            if ($token['type'] === 'number') {
                // Push numbers onto the stack
                $stack[] = $token['value'];
            } elseif ($token['type'] === 'operator') {
                // Operators pop values from the stack, perform the operation, and push the result
                if (count($stack) < 2) {
                    $this->log("Error: Not enough operands for operator {$token['value']}");
                    return 0;
                }

                $b = array_pop($stack);
                $a = array_pop($stack);

                switch ($token['value']) {
                    case '+':
                        $stack[] = $a + $b;
                        break;
                    case '-':
                        $stack[] = $a - $b;
                        break;
                    case '*':
                        $stack[] = $a * $b;
                        break;
                    case '/':
                        if ($b == 0) {
                            $this->log("Warning: Division by zero, using 1 instead");
                            $b = 1;
                        }
                        $stack[] = $a / $b;
                        break;
                }
            } elseif ($token['type'] === 'function_call') {
                // Functions pop values from the stack based on arg_count, perform the function, and push the result
                $arg_count = $token['arg_count'];

                if (count($stack) < $arg_count) {
                    $this->log("Error: Not enough arguments for function {$token['value']}");
                    return 0;
                }

                $args = array();
                for ($i = 0; $i < $arg_count; $i++) {
                    $args[] = array_pop($stack);
                }
                $args = array_reverse($args);

                switch ($token['value']) {
                    case 'ceil':
                        $stack[] = ceil($args[0]);
                        break;
                    case 'floor':
                        $stack[] = floor($args[0]);
                        break;
                    case 'round':
                        $stack[] = round($args[0]);
                        break;
                    case 'min':
                        $stack[] = min($args);
                        break;
                    case 'max':
                        $stack[] = max($args);
                        break;
                }
            }
        }

        if (count($stack) !== 1) {
            $this->log("Warning: Invalid expression, multiple values left on stack");
            return 0;
        }

        return $stack[0];
    }
}
