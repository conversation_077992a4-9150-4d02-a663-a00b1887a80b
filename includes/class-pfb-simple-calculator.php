<?php
/**
 * Simple calculator for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */
class PFB_Simple_Calculator {

    /**
     * Variables for calculation.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $variables    Variables for calculation.
     */
    private $variables = array();

    /**
     * Debug mode.
     *
     * @since    1.0.0
     * @access   private
     * @var      boolean    $debug    Whether to output debug information.
     */
    private $debug = true;

    /**
     * Field values from the form.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $field_values    Field values from the form.
     */
    private $field_values = array();

    /**
     * Direct variable references for the current calculation.
     * This is used to store variables that are directly referenced in the formula.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $direct_variables    Direct variable references.
     */
    private $direct_variables = array();

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     * @param    array    $variables     Price variables.
     * @param    boolean  $debug         Whether to output debug information.
     */
    public function __construct($variables = array(), $debug = true) {
        $this->variables = $variables;
        $this->debug = $debug;
    }

    /**
     * Log debug information.
     *
     * @since    1.0.0
     * @param    string    $message    Debug message.
     */
    private function log($message) {
        if ($this->debug) {
            error_log('[PFB Simple Calculator] ' . $message);
        }
    }

    /**
     * Calculate price based on formula and field values.
     *
     * @since    1.0.0
     * @param    string    $formula      Formula to evaluate.
     * @param    array     $field_values Field values from the form.
     * @return   float                   Calculated price.
     */
    public function calculate($formula, $field_values) {
        $this->log('Original formula: ' . $formula);
        $this->log('Field values: ' . print_r($field_values, true));
        $this->log('Variables: ' . print_r($this->variables, true));

        // Convert square brackets to parentheses for compatibility
        $formula = $this->convert_brackets_to_parentheses($formula);
        $this->log('Formula after converting brackets: ' . $formula);

        // Store field values for use in variable references
        $this->field_values = $field_values;

        // Extract direct variable references from the formula
        $this->extract_direct_variables($formula);

        // Replace field references with their values
        $formula = $this->replace_field_references($formula, $field_values);
        $this->log('Formula after replacing field references: ' . $formula);

        // Replace variable references with their values
        $formula = $this->replace_variable_references($formula);
        $this->log('Formula after replacing variable references: ' . $formula);

        // Process functions (ceil, floor, round, min, max)
        $formula = $this->process_functions($formula);
        $this->log('Formula after processing functions: ' . $formula);

        // Evaluate the final expression
        $result = $this->evaluate_expression($formula);
        $this->log('Final result: ' . $result);

        return $result;
    }

    /**
     * Convert square brackets to parentheses for compatibility.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula with square brackets.
     * @return   string                Formula with parentheses.
     */
    private function convert_brackets_to_parentheses($formula) {
        // Count opening and closing brackets to ensure they're balanced
        $open_count = substr_count($formula, '[');
        $close_count = substr_count($formula, ']');

        if ($open_count !== $close_count) {
            $this->log("Warning: Unbalanced brackets in formula: {$formula}");
        }

        // Replace square brackets with parentheses
        $formula = str_replace('[', '(', $formula);
        $formula = str_replace(']', ')', $formula);

        return $formula;
    }

    /**
     * Replace field references with their values.
     *
     * @since    1.0.0
     * @param    string    $formula      Formula with field references.
     * @param    array     $field_values Field values from the form.
     * @return   string                  Formula with field values.
     */
    private function replace_field_references($formula, $field_values) {
        // Match all field references {field_name}
        preg_match_all('/\{([^{}]+)\}/', $formula, $matches);

        foreach ($matches[0] as $index => $match) {
            $field_name = $matches[1][$index];

            if (isset($field_values[$field_name])) {
                $value = floatval($field_values[$field_name]);
                $formula = str_replace($match, $value, $formula);
                $this->log("Replaced field reference {$match} with {$value}");
            } else {
                $this->log("Warning: Field {$field_name} not found in form values");
                $formula = str_replace($match, '0', $formula);
            }
        }

        return $formula;
    }

    /**
     * Extract direct variable references from the formula.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula with variable references.
     */
    private function extract_direct_variables($formula) {
        // Match all variable references {variable_name}
        preg_match_all('/\{([^{}]+)\}/', $formula, $matches);

        foreach ($matches[0] as $index => $match) {
            $variable_name = $matches[1][$index];
            $this->direct_variables[$variable_name] = true;
            $this->log("Found direct variable reference: {$variable_name}");
        }
    }

    /**
     * Replace variable references with their values.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula with variable references.
     * @return   string                Formula with variable values.
     */
    private function replace_variable_references($formula) {
        // Match all variable references {variable_name}
        preg_match_all('/\{([^{}]+)\}/', $formula, $matches);

        foreach ($matches[0] as $index => $match) {
            $variable_name = $matches[1][$index];
            $value = 0; // Default value
            $found = false;

            // First check if the variable is directly in the field values
            if (isset($this->field_values[$variable_name])) {
                $value = floatval($this->field_values[$variable_name]);
                $found = true;
                $this->log("Found variable {$variable_name} directly in field values: {$value}");
            }
            // Then check if it's in the field values with pfb_var_ prefix
            else if (isset($this->field_values['pfb_var_' . $variable_name])) {
                $value = floatval($this->field_values['pfb_var_' . $variable_name]);
                $found = true;
                $this->log("Found variable {$variable_name} in field values with prefix: {$value}");
            }
            // Then check if it's in the variables array
            else if (isset($this->variables[$variable_name])) {
                $value = is_array($this->variables[$variable_name]) ?
                    floatval($this->variables[$variable_name]['value']) :
                    floatval($this->variables[$variable_name]);
                $found = true;
                $this->log("Found variable {$variable_name} in variables array: {$value}");
            }

            if ($found) {
                $formula = str_replace($match, $value, $formula);
                $this->log("Replaced variable reference {$match} with {$value}");
            } else {
                $this->log("Warning: Variable {$variable_name} not found in any source");
                $formula = str_replace($match, '0', $formula);
            }
        }

        return $formula;
    }

    /**
     * Process functions in the formula.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula with functions.
     * @return   string                Formula with functions evaluated.
     */
    private function process_functions($formula) {
        // Process ceil() function
        $formula = $this->process_function('ceil', $formula);

        // Process floor() function
        $formula = $this->process_function('floor', $formula);

        // Process round() function
        $formula = $this->process_function('round', $formula);

        // Process min() function
        $formula = $this->process_function('min', $formula);

        // Process max() function
        $formula = $this->process_function('max', $formula);

        return $formula;
    }

    /**
     * Process a specific function in the formula.
     *
     * @since    1.0.0
     * @param    string    $function_name    Function name.
     * @param    string    $formula          Formula with functions.
     * @return   string                      Formula with function evaluated.
     */
    private function process_function($function_name, $formula) {
        // Match function calls like ceil(expression) with proper handling of nested parentheses
        // This pattern uses a non-greedy match and handles balanced parentheses
        $pattern = '/' . $function_name . '\s*\(\s*((?:[^()]*|\([^()]*\))*)\s*\)/';

        // Keep track of processed functions to avoid infinite loops
        $processed = array();
        $iteration = 0;
        $max_iterations = 10; // Safety limit

        while (preg_match($pattern, $formula, $matches) && $iteration < $max_iterations) {
            $iteration++;
            $full_match = $matches[0];
            $expression = $matches[1];

            // Skip if we've already processed this exact match
            if (isset($processed[$full_match])) {
                break;
            }
            $processed[$full_match] = true;

            // Check for nested functions in the expression
            if (preg_match('/\b(ceil|floor|round|min|max)\s*\(/i', $expression)) {
                $this->log("Found nested function in {$function_name}({$expression})");

                // Process nested functions first
                $expression = $this->process_functions($expression);
                $this->log("After processing nested functions: {$expression}");
            }

            // Evaluate the expression inside the function
            $value = $this->evaluate_expression($expression);

            // Apply the function
            switch ($function_name) {
                case 'ceil':
                    $result = ceil($value);
                    break;
                case 'floor':
                    $result = floor($value);
                    break;
                case 'round':
                    $result = round($value);
                    break;
                case 'min':
                    // Split by comma and evaluate each part
                    $parts = explode(',', $expression);
                    $values = array();
                    foreach ($parts as $part) {
                        $values[] = $this->evaluate_expression(trim($part));
                    }
                    $result = min($values);
                    break;
                case 'max':
                    // Split by comma and evaluate each part
                    $parts = explode(',', $expression);
                    $values = array();
                    foreach ($parts as $part) {
                        $values[] = $this->evaluate_expression(trim($part));
                    }
                    $result = max($values);
                    break;
                default:
                    $result = $value;
            }

            $this->log("Processed {$function_name}({$expression}) = {$result}");

            // Replace the function call with its result
            $formula = str_replace($full_match, $result, $formula);
            $this->log("Formula after replacing {$function_name}: {$formula}");
        }

        if ($iteration >= $max_iterations) {
            $this->log("Warning: Maximum iterations reached while processing {$function_name} function");
        }

        return $formula;
    }

    /**
     * Evaluate a mathematical expression.
     *
     * @since    1.0.0
     * @param    string    $expression    Mathematical expression.
     * @return   float                    Result.
     */
    private function evaluate_expression($expression) {
        // Log the original expression
        $this->log("Evaluating expression: {$expression}");

        // Remove any remaining curly braces
        $expression = preg_replace('/\{([^{}]+)\}/', '0', $expression);

        // Check for nested parentheses and handle them properly
        if (preg_match('/\([^()]*\([^()]*\)[^()]*\)/', $expression)) {
            $this->log("Found nested parentheses in expression: {$expression}");

            // Process innermost parentheses first
            $pattern = '/\(([^()]*)\)/';
            while (preg_match($pattern, $expression, $matches)) {
                $inner_expr = $matches[1];
                $inner_result = $this->evaluate_expression($inner_expr);
                $expression = str_replace('(' . $inner_expr . ')', $inner_result, $expression);
                $this->log("After evaluating inner expression: {$expression}");
            }
        }

        // Ensure the expression is safe to evaluate
        $expression = $this->sanitize_expression($expression);
        $this->log("Sanitized expression: {$expression}");

        // Use PHP's built-in expression evaluation
        try {
            // Create a safe evaluation environment
            $result = 0;
            $code = '$result = ' . $expression . ';';

            // Check for division by zero
            if (preg_match('/\/\s*0/', $expression)) {
                $this->log("Warning: Division by zero detected in expression: {$expression}");
                return 0;
            }

            eval($code);
            $this->log("Expression result: {$result}");
            return floatval($result);
        } catch (Exception $e) {
            $this->log("Error evaluating expression: {$expression}, Error: {$e->getMessage()}");
            return 0;
        } catch (ParseError $e) {
            $this->log("Parse error evaluating expression: {$expression}, Error: {$e->getMessage()}");
            return 0;
        }
    }

    /**
     * Sanitize a mathematical expression.
     *
     * @since    1.0.0
     * @param    string    $expression    Mathematical expression.
     * @return   string                   Sanitized expression.
     */
    private function sanitize_expression($expression) {
        // Remove any potentially harmful code
        $expression = preg_replace('/[^0-9\+\-\*\/\(\)\.\,\s]/', '', $expression);

        // Fix common syntax issues
        $expression = str_replace('++', '+', $expression);
        $expression = str_replace('--', '+', $expression);
        $expression = str_replace('+-', '-', $expression);
        $expression = str_replace('-+', '-', $expression);

        // Ensure proper spacing
        $expression = preg_replace('/\s+/', ' ', $expression);

        // Handle empty expression
        if (trim($expression) === '') {
            return '0';
        }

        return $expression;
    }
}
