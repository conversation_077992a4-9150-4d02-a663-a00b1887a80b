<?php
/**
 * Currency operations for the plugin.
 *
 * @since      1.0.0
 */
class PFB_Currency {

    /**
     * Currency data.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $currency    Currency data.
     */
    private $currency;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    array    $currency    Currency data.
     */
    public function __construct($currency = null) {
        if ($currency) {
            $this->currency = $currency;
        } else {
            // Load default currency
            $this->currency = PFB_DB::get_default_currency();
        }
    }

    /**
     * Load currency by code.
     *
     * @since    1.0.0
     * @param    string   $code    Currency code.
     * @return   bool              True if currency was loaded, false otherwise.
     */
    public function load_by_code($code) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE code = %s", $code),
            ARRAY_A
        );

        if (!$currency) {
            return false;
        }

        $this->currency = $currency;

        return true;
    }

    /**
     * Get currency data.
     *
     * @since    1.0.0
     * @return   array    Currency data.
     */
    public function get_data() {
        return $this->currency;
    }

    /**
     * Get currency code.
     *
     * @since    1.0.0
     * @return   string    Currency code.
     */
    public function get_code() {
        return isset($this->currency['code']) ? $this->currency['code'] : '';
    }

    /**
     * Get currency symbol.
     *
     * @since    1.0.0
     * @return   string    Currency symbol.
     */
    public function get_symbol() {
        return isset($this->currency['symbol']) ? $this->currency['symbol'] : '';
    }

    /**
     * Get currency exchange rate.
     *
     * @since    1.0.0
     * @return   float    Exchange rate.
     */
    public function get_exchange_rate() {
        return isset($this->currency['exchange_rate']) ? floatval($this->currency['exchange_rate']) : 1;
    }

    /**
     * Get currency symbol position.
     *
     * @since    1.0.0
     * @return   string    Symbol position ('before' or 'after').
     */
    public function get_symbol_position() {
        return isset($this->currency['symbol_position']) ? $this->currency['symbol_position'] : 'before';
    }

    /**
     * Get currency RTL symbol position.
     *
     * @since    1.0.0
     * @return   string    RTL Symbol position ('before' or 'after').
     */
    public function get_rtl_symbol_position() {
        return isset($this->currency['rtl_symbol_position']) ? $this->currency['rtl_symbol_position'] : $this->get_symbol_position();
    }

    /**
     * Get currency thousand separator.
     *
     * @since    1.0.0
     * @return   string    Thousand separator.
     */
    public function get_thousand_separator() {
        return isset($this->currency['thousand_separator']) ? $this->currency['thousand_separator'] : ',';
    }

    /**
     * Get currency decimal separator.
     *
     * @since    1.0.0
     * @return   string    Decimal separator.
     */
    public function get_decimal_separator() {
        return isset($this->currency['decimal_separator']) ? $this->currency['decimal_separator'] : '.';
    }

    /**
     * Get currency decimal places.
     *
     * @since    1.0.0
     * @return   int    Number of decimal places.
     */
    public function get_decimal_places() {
        return isset($this->currency['decimal_places']) ? intval($this->currency['decimal_places']) : 2;
    }

    /**
     * Format price according to currency settings.
     *
     * @since    1.0.0
     * @param    float    $price    Price to format.
     * @return   string             Formatted price.
     */
    public function format_price($price, $is_rtl = false) {
        $price = floatval($price) * $this->get_exchange_rate();

        $symbol = $this->get_symbol();

        // Use RTL symbol position if in RTL mode and it's set
        $symbol_position = $is_rtl ? $this->get_rtl_symbol_position() : $this->get_symbol_position();

        $thousand_separator = isset($this->currency['thousand_separator']) ? $this->currency['thousand_separator'] : ',';
        $decimal_separator = isset($this->currency['decimal_separator']) ? $this->currency['decimal_separator'] : '.';
        $decimal_places = isset($this->currency['decimal_places']) ? intval($this->currency['decimal_places']) : 2;

        $price = number_format($price, $decimal_places, $decimal_separator, $thousand_separator);

        if ($symbol_position === 'before') {
            return $symbol . $price;
        } else {
            return $price . $symbol;
        }
    }

    /**
     * Convert price from one currency to another.
     *
     * @since    1.0.0
     * @param    float    $price           Price to convert.
     * @param    string   $from_currency   Source currency code.
     * @param    string   $to_currency     Target currency code.
     * @return   float                     Converted price.
     */
    public static function convert_price($price, $from_currency, $to_currency) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $from = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE code = %s", $from_currency),
            ARRAY_A
        );

        $to = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE code = %s", $to_currency),
            ARRAY_A
        );

        if (!$from || !$to) {
            return $price;
        }

        $from_rate = floatval($from['exchange_rate']);
        $to_rate = floatval($to['exchange_rate']);

        // Convert to base currency first, then to target currency
        return ($price / $from_rate) * $to_rate;
    }

    /**
     * Get all available currencies.
     *
     * @since    1.0.0
     * @return   array    Array of currencies.
     */
    public static function get_all_currencies() {
        return PFB_DB::get_currencies(array('number' => 100));
    }

    /**
     * Get default currency.
     *
     * @since    1.0.0
     * @return   array    Default currency.
     */
    public static function get_default_currency() {
        return PFB_DB::get_default_currency();
    }
}
