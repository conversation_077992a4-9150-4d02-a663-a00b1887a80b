<?php
/**
 * Update database schema to add RTL symbol position for currencies.
 *
 * This file contains functions to update the database schema to add RTL symbol position support.
 *
 * @since      1.0.0
 */

/**
 * Add rtl_symbol_position column to pfb_currencies table if it doesn't exist.
 *
 * @since    1.0.0
 * @return   bool    True on success, false on failure.
 */
function pfb_add_rtl_symbol_position_column() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_currencies';

    error_log('PFB: Checking if rtl_symbol_position column exists in table: ' . $table_name);

    // Check if the column already exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'rtl_symbol_position'");

    error_log('PFB: Column exists check result: ' . print_r($column_exists, true));

    if (empty($column_exists)) {
        error_log('PFB: rtl_symbol_position column does not exist, adding it now');

        // Add the column
        $sql = "ALTER TABLE {$table_name} ADD COLUMN rtl_symbol_position varchar(20) DEFAULT NULL AFTER symbol_position";
        error_log('PFB: Running SQL: ' . $sql);

        $result = $wpdb->query($sql);

        if ($result === false) {
            error_log('PFB: Failed to add rtl_symbol_position column to pfb_currencies table. Error: ' . $wpdb->last_error);
            return false;
        }

        error_log('PFB: Successfully added rtl_symbol_position column');

        // Update existing currencies to set rtl_symbol_position to match symbol_position
        $update_sql = "UPDATE {$table_name} SET rtl_symbol_position = symbol_position";
        $update_result = $wpdb->query($update_sql);

        if ($update_result === false) {
            error_log('PFB: Failed to update rtl_symbol_position values. Error: ' . $wpdb->last_error);
        } else {
            error_log('PFB: Successfully updated rtl_symbol_position values for ' . $update_result . ' currencies');
        }

        return true;
    }

    // Column already exists
    error_log('PFB: rtl_symbol_position column already exists');
    return true;
}

// Run the update function
pfb_add_rtl_symbol_position_column();
