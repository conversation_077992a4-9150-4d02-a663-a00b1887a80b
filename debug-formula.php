<?php
/**
 * Debug script for formula calculation
 *
 * This script helps debug formula calculation issues in the Price Form Builder plugin.
 * Place this file in the plugin directory and access it via the browser.
 */

// Load WordPress
require_once( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/wp-load.php' );

// Check if user is logged in and has admin privileges
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'You do not have sufficient permissions to access this page.' );
}

// Include required files
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-calculator.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-pfb-simple-calculator.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test formula calculation
function test_formula_calculation() {
    // Original formula with square brackets
    $original_formula = '0.2*[max(0,[{dropdown_1746574547071}-5000])/1000]';

    // Modified formula with parentheses
    $modified_formula = '0.2*(max(0,({dropdown_1746574547071}-5000)/1000))';

    // Alternative formula 1 - using parentheses only
    $alt_formula1 = '0.2*max(0,({dropdown_1746574547071}-5000)/1000)';

    // Alternative formula 2 - simplified
    $alt_formula2 = '0.2*max(0,({dropdown_1746574547071}-5000))/1000';

    // Test values
    $test_values = array(
        1000, 2000, 3000, 5000, 6000, 7000, 10000
    );

    echo '<h1>Formula Calculation Debug</h1>';

    echo '<h2>Original Formula</h2>';
    echo '<pre>' . htmlspecialchars($original_formula) . '</pre>';

    echo '<h2>Modified Formula</h2>';
    echo '<pre>' . htmlspecialchars($modified_formula) . '</pre>';

    echo '<h2>Alternative Formula 1</h2>';
    echo '<pre>' . htmlspecialchars($alt_formula1) . '</pre>';

    echo '<h2>Alternative Formula 2</h2>';
    echo '<pre>' . htmlspecialchars($alt_formula2) . '</pre>';

    echo '<h2>Test Calculations</h2>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr>
        <th>Dropdown Value</th>
        <th>Original Formula Result</th>
        <th>Modified Formula Result</th>
        <th>Alt Formula 1 Result</th>
        <th>Alt Formula 2 Result</th>
        <th>Manual Calculation</th>
    </tr>';

    // Initialize calculator
    $calculator = new PFB_Simple_Calculator(array(), true);

    foreach ($test_values as $value) {
        // Set up field values
        $field_values = array(
            'dropdown_1746574547071' => $value
        );

        // Calculate with original formula
        $original_result = $calculator->calculate($original_formula, $field_values);

        // Calculate with modified formula
        $modified_result = $calculator->calculate($modified_formula, $field_values);

        // Calculate with alternative formula 1
        $alt_result1 = $calculator->calculate($alt_formula1, $field_values);

        // Calculate with alternative formula 2
        $alt_result2 = $calculator->calculate($alt_formula2, $field_values);

        // Manual calculation
        $manual_result = 0;
        $diff = $value - 5000;
        if ($diff > 0) {
            $manual_result = 0.2 * ($diff / 1000);
        }

        echo '<tr>';
        echo '<td>' . $value . '</td>';
        echo '<td>' . $original_result . '</td>';
        echo '<td>' . $modified_result . '</td>';
        echo '<td>' . $alt_result1 . '</td>';
        echo '<td>' . $alt_result2 . '</td>';
        echo '<td>' . $manual_result . '</td>';
        echo '</tr>';
    }

    echo '</table>';

    echo '<h2>Debug Log</h2>';
    echo '<pre>';
    $log_file = ini_get('error_log');
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $pfb_log_lines = array_filter($log_lines, function($line) {
            return strpos($line, '[PFB') !== false;
        });
        echo implode("\n", array_slice($pfb_log_lines, -50));
    } else {
        echo "Log file not found or not accessible.";
    }
    echo '</pre>';
}

// Run the test
test_formula_calculation();
