/**
 * Admin styles for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */

/* General Styles */
.pfb-admin-wrap {
    font-family: 'Poppins', sans-serif;
    margin: 20px 20px 0 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.pfb-admin-header {
    background: #fff;
    color: #333;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
}

.pfb-admin-header h1 {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.pfb-admin-content {
    padding: 25px 30px;
}

.pfb-admin-footer {
    background: #fff;
    padding: 15px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

/* Tabs */
.pfb-tabs {
    display: flex;
    background: #fff;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.pfb-tab {
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #777;
    transition: all 0.2s ease;
    position: relative;
    margin-right: 5px;
}

.pfb-tab:hover {
    color: #333;
}

.pfb-tab.active {
    color: #2271b1;
}

.pfb-tab.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #2271b1;
}

.pfb-tab-content {
    display: none;
    padding: 15px 0;
}

.pfb-tab-content.active {
    display: block;
}

/* Forms */
.pfb-form {
    margin-bottom: 20px;
}

.pfb-form-group {
    margin-bottom: 20px;
}

.pfb-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 400;
    color: #1d2327;
    font-size: 14px;
}

.pfb-form-control {
    width: 100%;
    padding: 0 8px;
    height: 30px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 14px;
    transition: border-color 0.2s ease;
    background-color: #fff;
    color: #2c3338;
    /* Force LTR direction for input values */
    direction: ltr;
    text-align: left;
}

.pfb-form-control:focus {
    border-color: #2271b1;
    outline: none;
    box-shadow: 0 0 0 1px #2271b1;
}

textarea.pfb-form-control {
    height: auto;
    min-height: 60px;
    padding: 8px;
}

select.pfb-form-control {
    padding-right: 24px;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg width="20" height="20" xmlns="http://www.w3.org/2000/svg"><path d="M5 6l5 5 5-5 2 1-7 7-7-7 2-1z" fill="%23555"/></svg>');
    background-repeat: no-repeat;
    background-position: right 5px top 55%;
    background-size: 16px 16px;
    -webkit-appearance: none;
    appearance: none;
}

.pfb-form-help {
    margin-top: 4px;
    font-size: 12px;
    color: #646970;
    font-style: italic;
}

/* Buttons */
.pfb-btn {
    display: inline-block;
    padding: 0 12px;
    height: 30px;
    line-height: 28px;
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: 3px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 13px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
    text-align: center;
}

.pfb-btn-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
}

.pfb-btn-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.pfb-btn-secondary {
    background: #f6f7f7;
    color: #2c3338;
}

.pfb-btn-secondary:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

.pfb-btn-danger {
    background: #d63638;
    border-color: #d63638;
    color: #fff;
}

.pfb-btn-danger:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

/* Tables */
.pfb-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    border: 1px solid #c3c4c7;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-table th,
.pfb-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
    vertical-align: middle;
    font-size: 14px;
}

.pfb-table th {
    background: #fff;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #c3c4c7;
}

.pfb-table tr:nth-child(odd) {
    background-color: #f6f7f7;
}

.pfb-table tr:hover {
    background-color: #f0f0f1;
}

.pfb-table .pfb-actions {
    display: flex;
    gap: 8px;
}

/* Form Builder */
.pfb-builder {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

/* Width Setting Styles */
.pfb-width-setting-group {
    background-color: #f0f7ff;
    padding: 10px;
    border: 1px solid #72aee6;
    border-radius: 4px;
    margin-bottom: 15px;
}

.pfb-field-width-indicator {
    font-size: 12px;
    background-color: #f0f7ff;
    color: #2271b1;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 10px;
    border: 1px solid #72aee6;
    display: inline-block;
}

.pfb-builder-sidebar {
    width: 280px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-builder-content {
    flex: 1;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    min-height: 400px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-form-fields-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.pfb-form-fields-actions {
    display: flex;
    gap: 8px;
}

.pfb-builder-sidebar h3 {
    margin-top: 15px;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.pfb-field-types {
    margin-bottom: 15px;
}

.pfb-field-type {
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 13px;
}

.pfb-field-type:hover {
    background: #f6f7f7;
    border-color: #2271b1;
}

.pfb-field-type .dashicons {
    margin-right: 8px;
    color: #646970;
}

.pfb-field {
    padding: 12px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-bottom: 12px;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

/* Different background colors for different field types */
.pfb-field[data-type="text"] {
    background: linear-gradient(to right, #e6f2ff, #f0f7ff);
    border-left: 3px solid #72aee6;
}

.pfb-field[data-type="number"] {
    background: linear-gradient(to right, #e6fff2, #f0fff7);
    border-left: 3px solid #72e6b6;
}

.pfb-field[data-type="slider"] {
    background: linear-gradient(to right, #e6ffff, #f0ffff);
    border-left: 3px solid #72e6e6;
}

.pfb-field[data-type="dropdown"] {
    background: linear-gradient(to right, #fff2e6, #fff7f0);
    border-left: 3px solid #e6a872;
}

.pfb-field[data-type="radio"] {
    background: linear-gradient(to right, #f2e6ff, #f7f0ff);
    border-left: 3px solid #b672e6;
}

.pfb-field[data-type="checkbox"] {
    background: linear-gradient(to right, #fffae6, #fffaf0);
    border-left: 3px solid #e6d272;
}

.pfb-field[data-type="total"] {
    background: linear-gradient(to right, #e6e6ff, #f0f0ff);
    border-left: 3px solid #7272e6;
}

.pfb-field:hover {
    border-color: #2271b1;
}

.pfb-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.pfb-field-title {
    font-weight: 500;
    color: #1d2327;
    font-size: 14px;
}

.pfb-field-actions {
    display: flex;
    gap: 4px;
}

.pfb-field-action {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pfb-field-action:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
    color: #2271b1;
}

.pfb-field-settings {
    display: none;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #eee;
}

.pfb-field-settings.active {
    display: block;
}

.pfb-field-preview {
    margin-top: 8px;
    padding: 8px;
    background: #f6f7f7;
    border-radius: 4px;
    font-size: 13px;
}

/* Slider field styles */
.pfb-slider-preview {
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pfb-slider-control {
    width: 100%;
    margin-bottom: 8px;
}

.pfb-slider-value {
    font-weight: bold;
    color: #2271b1;
}

/* Force LTR for field options */
.pfb-field-option input,
.pfb-option-value-container input,
.pfb-option-label,
.pfb-option-value {
    direction: ltr !important;
    text-align: left !important;
}

.pfb-empty-form-message,
.pfb-empty-message {
    padding: 30px;
    text-align: center;
    color: #646970;
    background: #f6f7f7;
    border-radius: 4px;
    border: 1px dashed #c3c4c7;
    margin-bottom: 20px;
}

/* Price Variables */
.pfb-variable-category {
    margin-bottom: 25px;
}

.pfb-variable-category-header {
    background: #fff;
    padding: 10px 12px;
    border-radius: 4px;
    margin-bottom: 12px;
    color: #1d2327;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfb-variable-category-name {
    font-size: 15px;
    font-weight: 600;
}

.pfb-variable-category-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pfb-variable-category-count {
    font-size: 12px;
    color: #646970;
    font-weight: normal;
}

.pfb-variable-table-container {
    margin-bottom: 25px;
    overflow-x: auto;
}

.pfb-variable-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #c3c4c7;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-variable-table th,
.pfb-variable-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
    vertical-align: middle;
    font-size: 13px;
}

.pfb-variable-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.pfb-variable-table tr:hover {
    background-color: #f0f0f1;
}

.pfb-variable-name {
    font-weight: 600;
    color: #1d2327;
    font-size: 13px;
}

.pfb-variable-key code {
    font-family: Consolas, Monaco, monospace;
    background: #f6f7f7;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #3c434a;
    border: 1px solid #dcdcde;
}

.pfb-variable-type {
    color: #3c434a;
}

.pfb-variable-value {
    font-weight: 500;
    color: #2271b1;
}

.pfb-range-preview {
    display: inline-block;
    background: #f0f6fc;
    border: 1px solid #72aee6;
    border-radius: 3px;
    padding: 1px 6px;
    font-size: 11px;
    color: #2271b1;
}

.pfb-variable-actions {
    display: flex;
    gap: 4px;
    white-space: nowrap;
}

/* Currencies */
.pfb-currency-table-container {
    margin-bottom: 25px;
    overflow-x: auto;
}

.pfb-currency-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #c3c4c7;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-currency-table th,
.pfb-currency-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
    vertical-align: middle;
    font-size: 13px;
}

.pfb-currency-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.pfb-currency-row:hover {
    background-color: #f0f0f1;
}

.pfb-currency-row.default {
    background-color: rgba(34, 113, 177, 0.05);
}

.pfb-default-badge {
    display: inline-block;
    background: #2271b1;
    color: #fff;
    font-size: 11px;
    padding: 1px 6px;
    border-radius: 3px;
    margin-left: 6px;
    font-weight: normal;
    vertical-align: middle;
}

.pfb-currency-name {
    font-weight: 600;
    color: #1d2327;
    font-size: 13px;
}

.pfb-currency-code code {
    font-family: Consolas, Monaco, monospace;
    background: #f6f7f7;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #3c434a;
    border: 1px solid #dcdcde;
}

.pfb-currency-symbol {
    font-weight: bold;
    color: #3c434a;
}

.pfb-currency-rate {
    color: #2271b1;
}

.pfb-base-rate {
    font-weight: 600;
    color: #2271b1;
}

.pfb-currency-format {
    font-family: Consolas, Monaco, monospace;
    color: #3c434a;
}

.pfb-currency-actions {
    display: flex;
    gap: 4px;
    white-space: nowrap;
}

/* Settings */
.pfb-settings-section {
    margin-bottom: 25px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pfb-settings-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.pfb-language-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.pfb-language-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pfb-range-value {
    display: inline-block;
    margin-left: 8px;
    font-size: 13px;
    color: #646970;
}

/* Notifications */
.pfb-notification {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    border-left-width: 4px;
    border-left-style: solid;
}

.pfb-notification-success {
    background: #f0f6fc;
    border-left-color: #72aee6;
    color: #1d2327;
}

.pfb-notification-error {
    background: #fcf0f1;
    border-left-color: #d63638;
    color: #1d2327;
}

.pfb-notification-warning {
    background: #fcf9e8;
    border-left-color: #dba617;
    color: #1d2327;
}

.pfb-notification-info {
    background: #f0f6fc;
    border-left-color: #72aee6;
    color: #1d2327;
}

/* Modal */
.pfb-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.pfb-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.pfb-modal-header {
    padding: 15px;
    border-bottom: 1px solid #dcdcde;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfb-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.pfb-modal-close {
    color: #646970;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
}

.pfb-modal-body {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
}

.pfb-modal-footer {
    padding: 15px;
    border-top: 1px solid #dcdcde;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.pfb-price-range {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.pfb-field-options-header {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #646970;
}

.pfb-field-option-label-header {
    flex: 2;
    padding: 0 8px;
}

.pfb-field-option-value-header {
    flex: 1;
    padding: 0 8px;
}

.pfb-field-option-variable-header {
    flex: 2;
    padding: 0 8px;
}

.pfb-field-option-action-header {
    width: 30px;
}

.pfb-field-option {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.pfb-field-option .pfb-option-label {
    flex: 2;
}

.pfb-option-value-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.pfb-option-value-container .pfb-option-value {
    width: 100%;
}

.pfb-option-value-container .pfb-dynamic-value-button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    margin: 0;
    z-index: 1;
}

/* Formula Builder */
.pfb-formula-builder {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
}

.pfb-formula-input-container {
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Formula messages */
.pfb-formula-message {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 3px;
    font-size: 13px;
    display: none;
    animation: fadeIn 0.3s ease;
}

.pfb-formula-warning {
    background: #fcf9e8;
    border: 1px solid #dba617;
    color: #996600;
}

.pfb-formula-info {
    background: #e8f4fc;
    border: 1px solid #1786c3;
    color: #0c5c8a;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.pfb-clear-formula {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 8px;
    font-size: 12px;
    line-height: 1.5;
}

.pfb-formula-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    background: #f6f7f7;
    padding: 12px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.pfb-formula-section {
    flex: 1;
    min-width: 200px;
}

.pfb-formula-section-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d2327;
    font-size: 13px;
    padding-bottom: 4px;
    border-bottom: 1px solid #dcdcde;
}

.pfb-formula-fields,
.pfb-formula-operators,
.pfb-formula-numbers,
.pfb-formula-functions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.pfb-formula-field,
.pfb-formula-number,
.pfb-formula-operator,
.pfb-formula-function {
    display: inline-block;
    padding: 4px 8px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.pfb-formula-field {
    background: #e7f5ff;
    border-color: #72aee6;
    color: #2271b1;
}

.pfb-formula-variable, .pfb-formula-variable-item {
    background: #f0f0ff;
    border-color: #9e72e6;
    color: #5521b1;
}

.pfb-formula-variables-category {
    margin-bottom: 15px;
}

.pfb-formula-variables-category h4 {
    margin: 0 0 8px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #dcdcde;
    font-size: 14px;
}

.pfb-formula-variables-items {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.pfb-select-formula-variables {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.pfb-formula-number {
    background: #f0fff0;
    border-color: #72e672;
    color: #1d7a1d;
    font-weight: bold;
    min-width: 28px;
    text-align: center;
}

.pfb-formula-operator {
    background: #f6f7f7;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.pfb-formula-function {
    background: #f0f6fc;
    color: #2271b1;
}

.pfb-formula-field:hover,
.pfb-formula-variable:hover,
.pfb-formula-operator:hover,
.pfb-formula-function:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

.pfb-formula-empty {
    color: #646970;
    font-style: italic;
    font-size: 12px;
    padding: 5px 0;
}

/* Ensure formula box is always LTR */
.pfb-field-formula-input {
    direction: ltr !important;
    text-align: left !important;
    font-family: Consolas, Monaco, monospace !important;
}

/* Responsive */
@media (max-width: 992px) {
    .pfb-builder {
        flex-direction: column;
    }

    .pfb-builder-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }

    .pfb-modal-content {
        width: 90%;
        margin: 10% auto;
    }
}

@media (max-width: 782px) {
    .pfb-admin-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .pfb-admin-header h1 {
        margin-bottom: 15px;
    }

    .pfb-admin-actions {
        width: 100%;
        display: flex;
        justify-content: flex-start;
    }

    .pfb-tabs {
        flex-wrap: wrap;
    }

    .pfb-tab {
        flex: 1 0 auto;
        text-align: center;
        padding: 10px 12px;
        font-size: 13px;
    }

    .pfb-variable-list,
    .pfb-currency-list,
    .pfb-language-list {
        grid-template-columns: 1fr;
    }

    .pfb-form-group {
        margin-bottom: 15px;
    }

    .pfb-price-range,
    .pfb-field-option {
        flex-wrap: wrap;
    }

    .pfb-price-range input,
    .pfb-field-option input {
        flex: 1;
    }
}

/* Field Type Modal */
.pfb-field-types {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.pfb-field-type {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pfb-field-type:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pfb-field-type-icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: #2271b1;
}

.pfb-field-type-label {
    font-weight: 500;
    color: #1d2327;
}

/* Dynamic Value Modal */
.pfb-dynamic-value-search {
    margin-bottom: 15px;
}

.pfb-dynamic-value-categories {
    max-height: 400px;
    overflow-y: auto;
}

.pfb-dynamic-value-category {
    margin-bottom: 15px;
}

.pfb-dynamic-value-category-header {
    background: #f6f7f7;
    padding: 8px 12px;
    border: 1px solid #c3c4c7;
    border-radius: 4px 4px 0 0;
    font-weight: 600;
    color: #1d2327;
}

.pfb-dynamic-value-list {
    border: 1px solid #c3c4c7;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background: #fff;
}

.pfb-dynamic-value-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f1;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfb-dynamic-value-item:last-child {
    border-bottom: none;
}

.pfb-dynamic-value-item:hover {
    background: #f6f7f7;
}

.pfb-dynamic-value-name {
    font-weight: 500;
    color: #1d2327;
}

.pfb-dynamic-value-key {
    font-family: Consolas, Monaco, monospace;
    color: #2271b1;
    font-size: 12px;
}

.pfb-dynamic-value-loading {
    padding: 20px;
    text-align: center;
    color: #646970;
}

.pfb-dynamic-value-empty {
    padding: 15px;
    text-align: center;
    color: #646970;
    font-style: italic;
}

/* Dynamic Value Button */
.pfb-dynamic-value-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #f0f6fc;
    border: 1px solid #72aee6;
    color: #2271b1;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 5px;
}

.pfb-dynamic-value-button:hover {
    background: #e0f0ff;
    border-color: #2271b1;
}

/* Conditional Logic Styles */
.pfb-field-conditional-indicator {
    background: #d63638;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 10px;
    display: inline-block;
}

.pfb-conditional-logic-section {
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 15px;
}

.pfb-conditional-logic-settings {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.pfb-conditional-logic-settings .pfb-form-group {
    margin-bottom: 15px;
}

.pfb-conditional-logic-settings .pfb-form-group:last-child {
    margin-bottom: 0;
}

.pfb-conditional-conditions {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background: white;
}

.pfb-conditional-condition {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 4px;
}

.pfb-conditional-condition:last-child {
    margin-bottom: 0;
}

.pfb-conditional-condition select,
.pfb-conditional-condition input {
    flex: 1;
}

.pfb-conditional-condition .pfb-btn {
    flex: none;
    width: auto;
    min-width: 30px;
    padding: 5px 8px;
}

.pfb-conditional-action,
.pfb-conditional-operator,
.pfb-condition-field,
.pfb-condition-operator,
.pfb-condition-value {
    font-size: 13px;
}

.pfb-enable-conditional-logic {
    margin-right: 8px;
}

.pfb-conditional-logic-section .pfb-form-help {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pfb-builder {
        flex-direction: column;
    }

    .pfb-builder-sidebar {
        width: 100%;
        order: 2;
    }

    .pfb-admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .pfb-admin-header .pfb-btn {
        align-self: flex-end;
    }

    .pfb-table {
        font-size: 12px;
    }

    .pfb-table th,
    .pfb-table td {
        padding: 6px 8px;
    }

    .pfb-form-group {
        margin-bottom: 15px;
    }

    .pfb-field {
        padding: 10px;
    }

    .pfb-field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .pfb-field-actions {
        align-self: flex-end;
    }

    .pfb-conditional-condition {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .pfb-conditional-condition select,
    .pfb-conditional-condition input {
        width: 100%;
    }
}

/* Beautiful Save Popup Styles */
.pfb-save-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: none;
    backdrop-filter: blur(5px);
}

.pfb-save-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 500px;
    max-width: 90vw;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.pfb-save-popup.pfb-popup-show {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

.pfb-popup-header {
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e5e5e5;
    position: relative;
}

.pfb-popup-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e1e1e;
    display: flex;
    align-items: center;
    gap: 10px;
}

.pfb-popup-header .dashicons {
    color: #0073aa;
    font-size: 24px;
}

.pfb-popup-close {
    position: absolute;
    top: 20px;
    right: 25px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.pfb-popup-close:hover {
    background: #f0f0f0;
    color: #333;
}

.pfb-popup-content {
    padding: 30px;
}

.pfb-save-validation {
    margin-bottom: 20px;
}

.pfb-validation-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #00a32a;
}

.pfb-validation-icon {
    color: #00a32a;
    font-size: 18px;
}

.pfb-validation-text {
    color: #1e1e1e;
    font-size: 14px;
}

.pfb-save-progress {
    text-align: center;
    padding: 20px 0;
}

.pfb-progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.pfb-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005177);
    width: 0%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.pfb-progress-text {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.pfb-save-result {
    text-align: center;
    padding: 20px 0;
}

.pfb-result-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.pfb-result-icon.pfb-success {
    color: #00a32a;
}

.pfb-result-icon.pfb-error {
    color: #d63638;
}

.pfb-result-text {
    font-size: 16px;
    color: #1e1e1e;
    margin: 0;
}

.pfb-popup-footer {
    padding: 20px 30px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.pfb-popup-footer .pfb-btn {
    min-width: 120px;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

/* Enhanced Conditional Logic Styles */
.pfb-conditional-section {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    background: #fafafa;
}

.pfb-section-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e1e1e;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pfb-toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    margin-bottom: 10px;
}

.pfb-toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    transition: background 0.3s ease;
}

.pfb-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.pfb-enable-conditional-logic:checked + .pfb-toggle-slider {
    background: #0073aa;
}

.pfb-enable-conditional-logic:checked + .pfb-toggle-slider::before {
    transform: translateX(26px);
}

.pfb-toggle-text {
    font-weight: 500;
    color: #1e1e1e;
}

.pfb-conditional-rule-builder {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e5e5e5;
}

.pfb-rule-sentence {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.pfb-rule-text {
    color: #666;
    font-weight: 500;
}

.pfb-conditions-container {
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 15px;
    background: #f9f9f9;
}

.pfb-condition-row {
    margin-bottom: 15px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e5e5;
}

.pfb-condition-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 10px;
    align-items: center;
}

.pfb-btn-outline {
    background: transparent;
    border: 2px solid #0073aa;
    color: #0073aa;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pfb-btn-outline:hover {
    background: #0073aa;
    color: white;
}

.pfb-btn-danger {
    background: #d63638;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.pfb-btn-danger:hover {
    background: #b32d2e;
}

/* Mobile responsive for new styles */
@media (max-width: 768px) {
    .pfb-save-popup {
        width: 95vw;
        margin: 20px;
    }

    .pfb-condition-inputs {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .pfb-rule-sentence {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
