/**
 * Admin styles for the Subtotal Field in Price Form Builder plugin.
 *
 * @since      1.0.0
 */

/* Subtotal Field Settings */
.pfb-field[data-type="subtotal"] {
    background: linear-gradient(to right, #f0e6ff, #f7f0ff);
    border-left: 3px solid #8E54E9;
}

.pfb-subtotal-lines-header {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    padding: 8px 10px;
    background: #f6f7f7;
    border-radius: 6px;
    border: 1px solid #dcdcde;
}

.pfb-subtotal-line-label-header {
    flex: 1;
    font-size: 13px;
    font-weight: 600;
    color: #1d2327;
}

.pfb-subtotal-line-formula-header {
    flex: 2;
    font-size: 13px;
    font-weight: 600;
    color: #1d2327;
}

.pfb-subtotal-line-action-header {
    width: 30px;
}

.pfb-subtotal-lines {
    border: 1px solid #dcdcde;
    border-radius: 6px;
    padding: 15px;
    background: #fff;
    margin-bottom: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.pfb-subtotal-line {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    padding: 12px;
    border-bottom: 1px solid #f0f0f1;
    align-items: flex-start;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.pfb-subtotal-line:hover {
    background-color: rgba(142, 84, 233, 0.03);
}

.pfb-subtotal-line.active-line {
    background-color: rgba(142, 84, 233, 0.08);
    border-left: 3px solid #8E54E9;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pfb-subtotal-line:last-child {
    margin-bottom: 0;
    padding-bottom: 12px;
    border-bottom: none;
}

.pfb-subtotal-line-label {
    flex: 1;
}

.pfb-formula-input-container {
    flex: 2;
    position: relative;
}

.pfb-subtotal-line-formula {
    width: 100%;
    min-height: 60px;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    padding: 8px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    transition: all 0.2s ease;
    resize: vertical;
}

.pfb-subtotal-line-formula:focus {
    border-color: #8E54E9;
    box-shadow: 0 0 0 1px #8E54E9;
    outline: none;
}

.pfb-select-subtotal-formula {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #f0e6ff;
    border: 1px solid #8E54E9;
    color: #8E54E9;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pfb-select-subtotal-formula:hover {
    background: #e6d9ff;
}

.pfb-add-subtotal-line,
.pfb-remove-subtotal-line {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
}

.pfb-add-subtotal-line {
    background: #f0f6fc;
    border-color: #72aee6;
    color: #2271b1;
}

.pfb-add-subtotal-line:hover {
    background: #e0f0ff;
    border-color: #2271b1;
}

.pfb-remove-subtotal-line {
    background: #fcf0f1;
    border-color: #d63638;
    color: #d63638;
}

.pfb-remove-subtotal-line:hover {
    background: #ffd8d9;
    border-color: #b32d2e;
}

/* Formula Tools */
.pfb-formula-tools {
    background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
    border: 1px solid #dcdcde;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.pfb-formula-tools:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #4776E6 0%, #8E54E9 100%);
}

.pfb-formula-section {
    margin-bottom: 15px;
}

.pfb-formula-section:last-child {
    margin-bottom: 0;
}

.pfb-formula-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #dcdcde;
}

.pfb-formula-fields,
.pfb-formula-variables,
.pfb-formula-operators,
.pfb-formula-functions,
.pfb-formula-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.pfb-formula-field,
.pfb-formula-variable,
.pfb-formula-operator,
.pfb-formula-function,
.pfb-formula-number {
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.pfb-formula-field {
    background: #e7f5ff;
    border: 1px solid #72aee6;
    color: #2271b1;
}

.pfb-formula-field:hover {
    background: #d0ebff;
    border-color: #2271b1;
}

.pfb-formula-variable {
    background: #f0e6ff;
    border: 1px solid #b672e6;
    color: #8E54E9;
}

.pfb-formula-variable:hover {
    background: #e6d9ff;
    border-color: #8E54E9;
}

.pfb-formula-operator {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    color: #1d2327;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.pfb-formula-operator:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

.pfb-formula-function {
    background: #f0f6fc;
    border: 1px solid #72aee6;
    color: #2271b1;
}

.pfb-formula-function:hover {
    background: #e0f0ff;
    border-color: #2271b1;
}

.pfb-formula-number {
    background: #f0fff0;
    border: 1px solid #72e672;
    color: #1d7a1d;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.pfb-formula-number:hover {
    background: #e0ffe0;
    border-color: #1d7a1d;
}

.pfb-clear-formula {
    background: #fcf0f1;
    border: 1px solid #d63638;
    color: #d63638;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.pfb-clear-formula:hover {
    background: #ffd8d9;
    border-color: #b32d2e;
}

/* Empty Value Display */
.pfb-subtotal-empty-value-input {
    background: #f9f9f9;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #1d2327;
    transition: all 0.2s ease;
}

.pfb-subtotal-empty-value-input:focus {
    border-color: #8E54E9;
    box-shadow: 0 0 0 1px #8E54E9;
    outline: none;
    background: #fff;
}
