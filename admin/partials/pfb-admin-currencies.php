<?php
/**
 * Admin currencies template.
 *
 * @since      1.0.0
 */
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1><?php _e('Currencies', 'price-form-builder'); ?></h1>
        <div class="pfb-admin-actions">
            <button id="pfb-add-currency" class="pfb-btn pfb-btn-primary">
                <span class="dashicons dashicons-plus"></span> <?php _e('Add New Currency', 'price-form-builder'); ?>
            </button>
        </div>
    </div>

    <div class="pfb-admin-content">
        <div id="pfb-currencies-container">
            <?php
            // Get currencies
            $currencies = PFB_Currency::get_all_currencies();

            if (empty($currencies)) {
                echo '<div class="pfb-empty-message">';
                echo '<p>' . __('No currencies found. Click "Add New Currency" to create one.', 'price-form-builder') . '</p>';
                echo '</div>';
            } else {
                ?>
                <div class="pfb-currency-table-container">
                    <table class="pfb-currency-table">
                        <thead>
                            <tr>
                                <th><?php _e('Name', 'price-form-builder'); ?></th>
                                <th><?php _e('Code', 'price-form-builder'); ?></th>
                                <th><?php _e('Symbol', 'price-form-builder'); ?></th>
                                <th><?php _e('Exchange Rate', 'price-form-builder'); ?></th>
                                <th><?php _e('Format', 'price-form-builder'); ?></th>
                                <th><?php _e('Actions', 'price-form-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($currencies as $currency) :
                                $is_default = $currency['is_default'] == 1;
                            ?>
                                <tr class="pfb-currency-row <?php echo $is_default ? 'default' : ''; ?>" data-currency-id="<?php echo esc_attr($currency['id']); ?>">
                                    <td class="pfb-currency-name">
                                        <?php echo esc_html($currency['name']); ?>
                                        <?php if ($is_default) : ?>
                                            <span class="pfb-default-badge"><?php _e('Default', 'price-form-builder'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="pfb-currency-code">
                                        <code><?php echo esc_html($currency['code']); ?></code>
                                    </td>
                                    <td class="pfb-currency-symbol">
                                        <?php echo esc_html($currency['symbol']); ?>
                                    </td>
                                    <td class="pfb-currency-rate">
                                        <?php if ($is_default) : ?>
                                            <span class="pfb-base-rate">1.0</span>
                                        <?php else : ?>
                                            <?php echo esc_html('1 ' . PFB_Currency::get_default_currency()['code'] . ' = ' . $currency['exchange_rate'] . ' ' . $currency['code']); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="pfb-currency-format">
                                        <?php
                                        $symbol = $currency['symbol'];
                                        $example = '1,234.56';
                                        if ($currency['symbol_position'] === 'before') {
                                            echo esc_html($symbol . $example);
                                        } else {
                                            echo esc_html($example . $symbol);
                                        }
                                        ?>
                                    </td>
                                    <td class="pfb-currency-actions">
                                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-currency">
                                            <span class="dashicons dashicons-edit"></span>
                                        </button>
                                        <?php if (!$is_default) : ?>
                                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-set-default-currency" title="<?php _e('Set as Default', 'price-form-builder'); ?>">
                                                <span class="dashicons dashicons-star-filled"></span>
                                            </button>
                                            <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-currency" title="<?php _e('Delete', 'price-form-builder'); ?>">
                                                <span class="dashicons dashicons-trash"></span>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
</div>

<!-- Currency Modal -->
<div id="pfb-currency-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2 id="pfb-currency-modal-title"><?php _e('Add New Currency', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <form id="pfb-currency-form">
                <input type="hidden" id="currency_id" value="">

                <div class="pfb-form-group">
                    <label for="currency_name"><?php _e('Currency Name', 'price-form-builder'); ?></label>
                    <input type="text" id="currency_name" class="pfb-form-control" required>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_code"><?php _e('Currency Code', 'price-form-builder'); ?></label>
                    <input type="text" id="currency_code" class="pfb-form-control" required>
                    <div class="pfb-form-help"><?php _e('ISO 4217 currency code (e.g., USD, EUR, GBP).', 'price-form-builder'); ?></div>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_symbol"><?php _e('Currency Symbol', 'price-form-builder'); ?></label>
                    <input type="text" id="currency_symbol" class="pfb-form-control" required>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_exchange_rate"><?php _e('Exchange Rate', 'price-form-builder'); ?></label>
                    <input type="number" id="currency_exchange_rate" class="pfb-form-control" step="0.000001" min="0.000001" required>
                    <div class="pfb-form-help">
                        <?php echo esc_html('1 ' . (isset(PFB_Currency::get_default_currency()['code']) ? PFB_Currency::get_default_currency()['code'] : 'USD') . ' = ? ' . __('of this currency', 'price-form-builder')); ?>
                    </div>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_symbol_position"><?php _e('Symbol Position (LTR)', 'price-form-builder'); ?></label>
                    <select id="currency_symbol_position" class="pfb-form-control">
                        <option value="before"><?php _e('Before amount', 'price-form-builder'); ?></option>
                        <option value="after"><?php _e('After amount', 'price-form-builder'); ?></option>
                    </select>
                    <div class="pfb-form-help"><?php _e('Position of the currency symbol in Left-to-Right (LTR) mode.', 'price-form-builder'); ?></div>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_rtl_symbol_position"><?php _e('Symbol Position (RTL)', 'price-form-builder'); ?></label>
                    <select id="currency_rtl_symbol_position" class="pfb-form-control">
                        <option value="before"><?php _e('Before amount', 'price-form-builder'); ?></option>
                        <option value="after"><?php _e('After amount', 'price-form-builder'); ?></option>
                    </select>
                    <div class="pfb-form-help"><?php _e('Position of the currency symbol in Right-to-Left (RTL) mode.', 'price-form-builder'); ?></div>
                </div>

                <div class="pfb-form-group">
                    <label for="currency_thousand_separator"><?php _e('Thousand Separator', 'price-form-builder'); ?></label>
                    <input type="text" id="currency_thousand_separator" class="pfb-form-control" value=",">
                </div>

                <div class="pfb-form-group">
                    <label for="currency_decimal_separator"><?php _e('Decimal Separator', 'price-form-builder'); ?></label>
                    <input type="text" id="currency_decimal_separator" class="pfb-form-control" value=".">
                </div>

                <div class="pfb-form-group">
                    <label for="currency_decimal_places"><?php _e('Decimal Places', 'price-form-builder'); ?></label>
                    <input type="number" id="currency_decimal_places" class="pfb-form-control" value="2" min="0" max="6">
                </div>

                <div class="pfb-form-group">
                    <label>
                        <input type="checkbox" id="currency_is_default"> <?php _e('Set as default currency', 'price-form-builder'); ?>
                    </label>
                </div>
            </form>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
            <button type="button" class="pfb-btn pfb-btn-primary pfb-modal-save"><?php _e('Save', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Open currency modal
        $('#pfb-add-currency').on('click', function() {
            $('#pfb-currency-modal-title').text('Add New Currency');
            $('#pfb-currency-form')[0].reset();
            $('#currency_id').val('');
            $('#currency_exchange_rate').val('1');
            $('#currency_symbol_position').val('before');
            $('#currency_rtl_symbol_position').val('before');
            $('#currency_thousand_separator').val(',');
            $('#currency_decimal_separator').val('.');
            $('#currency_decimal_places').val('2');
            $('#currency_is_default').prop('checked', false);

            $('#pfb-currency-modal').show();
        });

        // Close modal
        $('.pfb-modal-close, .pfb-modal-cancel').on('click', function() {
            $('#pfb-currency-modal').hide();
        });

        // Save currency
        $('#pfb-currency-modal .pfb-modal-save').on('click', function() {
            const currencyData = {
                id: $('#currency_id').val(),
                name: $('#currency_name').val(),
                code: $('#currency_code').val(),
                symbol: $('#currency_symbol').val(),
                exchange_rate: $('#currency_exchange_rate').val(),
                symbol_position: $('#currency_symbol_position').val(),
                rtl_symbol_position: $('#currency_rtl_symbol_position').val(),
                thousand_separator: $('#currency_thousand_separator').val(),
                decimal_separator: $('#currency_decimal_separator').val(),
                decimal_places: $('#currency_decimal_places').val(),
                is_default: $('#currency_is_default').is(':checked') ? 1 : 0
            };

            // Validate form
            if (!currencyData.name || !currencyData.code || !currencyData.symbol || !currencyData.exchange_rate) {
                alert('Please fill in all required fields.');
                return;
            }

            // Save currency via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_save_currency',
                    nonce: pfb_data.nonce,
                    currency_data: currencyData
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Edit currency
        $('.pfb-edit-currency').on('click', function() {
            const currencyId = $(this).closest('.pfb-currency-row').data('currency-id');

            // Get currency data via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'GET',
                data: {
                    action: 'pfb_get_currency',
                    nonce: pfb_data.nonce,
                    currency_id: currencyId
                },
                success: function(response) {
                    if (response.success) {
                        const currency = response.data.currency;

                        $('#pfb-currency-modal-title').text('Edit Currency');
                        $('#currency_id').val(currency.id);
                        $('#currency_name').val(currency.name);
                        $('#currency_code').val(currency.code);
                        $('#currency_symbol').val(currency.symbol);
                        $('#currency_exchange_rate').val(currency.exchange_rate);
                        $('#currency_symbol_position').val(currency.symbol_position);
                        $('#currency_rtl_symbol_position').val(currency.rtl_symbol_position || currency.symbol_position); // Default to regular position if RTL not set
                        $('#currency_thousand_separator').val(currency.thousand_separator);
                        $('#currency_decimal_separator').val(currency.decimal_separator);
                        $('#currency_decimal_places').val(currency.decimal_places);
                        $('#currency_is_default').prop('checked', currency.is_default == 1);

                        $('#pfb-currency-modal').show();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Set default currency
        $('.pfb-set-default-currency').on('click', function() {
            const currencyId = $(this).closest('.pfb-currency-row').data('currency-id');

            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_set_default_currency',
                    nonce: pfb_data.nonce,
                    currency_id: currencyId
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Delete currency
        $('.pfb-delete-currency').on('click', function() {
            if (confirm(pfb_data.i18n.confirm_delete)) {
                const currencyId = $(this).closest('.pfb-currency-row').data('currency-id');

                $.ajax({
                    url: pfb_data.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'pfb_delete_currency',
                        nonce: pfb_data.nonce,
                        currency_id: currencyId
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data.message);
                        }
                    }
                });
            }
        });
    });
</script>
