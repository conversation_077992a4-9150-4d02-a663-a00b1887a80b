<?php
/**
 * Admin price sheet template.
 *
 * @since      1.0.0
 */

// Get category ID from URL
$category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;

// Get category
$category = null;
if ($category_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'pfb_price_categories';
    $category = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $category_id),
        ARRAY_A
    );
}

// Get variables
$variables = array();
if ($category) {
    $variables = PFB_DB::get_price_variables(array('category_id' => $category_id));
}
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1>
            <?php _e('Price Sheet', 'price-form-builder'); ?>
            <?php if ($category) : ?>
                - <?php echo esc_html($category['name']); ?>
            <?php endif; ?>
        </h1>
        <div class="pfb-admin-actions">
            <?php if ($category) : ?>
                <button id="pfb-save-price-sheet" class="pfb-btn pfb-btn-primary">
                    <span class="dashicons dashicons-saved"></span> <?php _e('Save Changes', 'price-form-builder'); ?>
                </button>
            <?php endif; ?>
            <a href="<?php echo admin_url('admin.php?page=pfb-price-variables'); ?>" class="pfb-btn pfb-btn-secondary">
                <span class="dashicons dashicons-arrow-left-alt"></span> <?php _e('Back to Variables', 'price-form-builder'); ?>
            </a>
        </div>
    </div>
    
    <div class="pfb-admin-content">
        <?php if (!$category) : ?>
            <div class="pfb-empty-message">
                <p><?php _e('Please select a category to edit its price variables.', 'price-form-builder'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=pfb-price-variables'); ?>" class="pfb-btn pfb-btn-primary">
                    <?php _e('Go to Categories', 'price-form-builder'); ?>
                </a>
            </div>
        <?php else : ?>
            <div class="pfb-price-sheet-container">
                <form id="pfb-price-sheet-form">
                    <input type="hidden" name="category_id" value="<?php echo esc_attr($category_id); ?>">
                    
                    <div class="pfb-price-sheet-toolbar">
                        <button type="button" id="pfb-add-variable-row" class="pfb-btn pfb-btn-secondary">
                            <span class="dashicons dashicons-plus"></span> <?php _e('Add Variable', 'price-form-builder'); ?>
                        </button>
                        
                        <div class="pfb-price-sheet-filters">
                            <input type="text" id="pfb-price-sheet-search" class="pfb-form-control" placeholder="<?php _e('Search variables...', 'price-form-builder'); ?>">
                        </div>
                    </div>
                    
                    <div class="pfb-price-sheet-table-container">
                        <table class="pfb-price-sheet-table">
                            <thead>
                                <tr>
                                    <th class="pfb-price-sheet-name"><?php _e('Name', 'price-form-builder'); ?></th>
                                    <th class="pfb-price-sheet-key"><?php _e('Variable Key', 'price-form-builder'); ?></th>
                                    <th class="pfb-price-sheet-type"><?php _e('Type', 'price-form-builder'); ?></th>
                                    <th class="pfb-price-sheet-value"><?php _e('Value', 'price-form-builder'); ?></th>
                                    <th class="pfb-price-sheet-actions"><?php _e('Actions', 'price-form-builder'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($variables)) : ?>
                                    <tr class="pfb-price-sheet-empty-row">
                                        <td colspan="5"><?php _e('No variables found. Add your first variable using the button above.', 'price-form-builder'); ?></td>
                                    </tr>
                                <?php else : ?>
                                    <?php foreach ($variables as $variable) : ?>
                                        <tr class="pfb-price-sheet-row" data-variable-id="<?php echo esc_attr($variable['id']); ?>">
                                            <td class="pfb-price-sheet-name">
                                                <input type="text" name="variables[<?php echo esc_attr($variable['id']); ?>][name]" class="pfb-form-control" value="<?php echo esc_attr($variable['name']); ?>">
                                            </td>
                                            <td class="pfb-price-sheet-key">
                                                <input type="text" name="variables[<?php echo esc_attr($variable['id']); ?>][variable_key]" class="pfb-form-control" value="<?php echo esc_attr($variable['variable_key']); ?>">
                                            </td>
                                            <td class="pfb-price-sheet-type">
                                                <select name="variables[<?php echo esc_attr($variable['id']); ?>][price_type]" class="pfb-form-control pfb-price-type-select">
                                                    <option value="fixed" <?php selected($variable['price_type'], 'fixed'); ?>><?php _e('Fixed', 'price-form-builder'); ?></option>
                                                    <option value="range" <?php selected($variable['price_type'], 'range'); ?>><?php _e('Range', 'price-form-builder'); ?></option>
                                                </select>
                                            </td>
                                            <td class="pfb-price-sheet-value">
                                                <?php if ($variable['price_type'] === 'fixed') : ?>
                                                    <input type="number" name="variables[<?php echo esc_attr($variable['id']); ?>][price_value]" class="pfb-form-control pfb-price-value-input" value="<?php echo esc_attr($variable['price_value']); ?>" step="0.01">
                                                <?php else : ?>
                                                    <?php 
                                                    $ranges = maybe_unserialize($variable['price_ranges']);
                                                    if (!empty($ranges) && is_array($ranges)) {
                                                        echo '<button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-ranges" data-variable-id="' . esc_attr($variable['id']) . '">';
                                                        echo '<span class="dashicons dashicons-edit"></span> ' . count($ranges) . ' ' . __('ranges', 'price-form-builder');
                                                        echo '</button>';
                                                        
                                                        // Hidden input to store ranges
                                                        echo '<input type="hidden" name="variables[' . esc_attr($variable['id']) . '][price_ranges]" class="pfb-price-ranges-input" value="' . esc_attr(json_encode($ranges)) . '">';
                                                    } else {
                                                        echo '<button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-ranges" data-variable-id="' . esc_attr($variable['id']) . '">';
                                                        echo '<span class="dashicons dashicons-plus"></span> ' . __('Add ranges', 'price-form-builder');
                                                        echo '</button>';
                                                        
                                                        // Hidden input to store ranges
                                                        echo '<input type="hidden" name="variables[' . esc_attr($variable['id']) . '][price_ranges]" class="pfb-price-ranges-input" value="[]">';
                                                    }
                                                    ?>
                                                <?php endif; ?>
                                            </td>
                                            <td class="pfb-price-sheet-actions">
                                                <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-variable-row">
                                                    <span class="dashicons dashicons-trash"></span>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Ranges Modal -->
<div id="pfb-ranges-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2 id="pfb-ranges-modal-title"><?php _e('Edit Price Ranges', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <div id="pfb-ranges-container">
                <div class="pfb-ranges-header">
                    <div class="pfb-range-min-header"><?php _e('Min Quantity', 'price-form-builder'); ?></div>
                    <div class="pfb-range-max-header"><?php _e('Max Quantity', 'price-form-builder'); ?></div>
                    <div class="pfb-range-price-header"><?php _e('Price', 'price-form-builder'); ?></div>
                    <div class="pfb-range-action-header"></div>
                </div>
                <div id="pfb-ranges-list">
                    <!-- Ranges will be added here -->
                </div>
                <button type="button" id="pfb-add-range" class="pfb-btn pfb-btn-secondary">
                    <span class="dashicons dashicons-plus"></span> <?php _e('Add Range', 'price-form-builder'); ?>
                </button>
            </div>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
            <button type="button" class="pfb-btn pfb-btn-primary pfb-save-ranges"><?php _e('Save Ranges', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Current variable ID for ranges modal
        let currentVariableId = null;
        
        // Add variable row
        $('#pfb-add-variable-row').on('click', function() {
            const newId = 'new_' + Date.now();
            const $newRow = $(`
                <tr class="pfb-price-sheet-row" data-variable-id="${newId}">
                    <td class="pfb-price-sheet-name">
                        <input type="text" name="variables[${newId}][name]" class="pfb-form-control" value="">
                    </td>
                    <td class="pfb-price-sheet-key">
                        <input type="text" name="variables[${newId}][variable_key]" class="pfb-form-control" value="">
                    </td>
                    <td class="pfb-price-sheet-type">
                        <select name="variables[${newId}][price_type]" class="pfb-form-control pfb-price-type-select">
                            <option value="fixed"><?php _e('Fixed', 'price-form-builder'); ?></option>
                            <option value="range"><?php _e('Range', 'price-form-builder'); ?></option>
                        </select>
                    </td>
                    <td class="pfb-price-sheet-value">
                        <input type="number" name="variables[${newId}][price_value]" class="pfb-form-control pfb-price-value-input" value="0" step="0.01">
                    </td>
                    <td class="pfb-price-sheet-actions">
                        <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-variable-row">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </td>
                </tr>
            `);
            
            // Remove empty row if it exists
            $('.pfb-price-sheet-empty-row').remove();
            
            // Add new row
            $('.pfb-price-sheet-table tbody').append($newRow);
            
            // Focus on the name field
            $newRow.find('input').first().focus();
        });
        
        // Delete variable row
        $(document).on('click', '.pfb-delete-variable-row', function() {
            if (confirm('<?php _e('Are you sure you want to delete this variable?', 'price-form-builder'); ?>')) {
                $(this).closest('tr').remove();
                
                // Add empty row if no variables left
                if ($('.pfb-price-sheet-row').length === 0) {
                    $('.pfb-price-sheet-table tbody').html(`
                        <tr class="pfb-price-sheet-empty-row">
                            <td colspan="5"><?php _e('No variables found. Add your first variable using the button above.', 'price-form-builder'); ?></td>
                        </tr>
                    `);
                }
            }
        });
        
        // Price type change
        $(document).on('change', '.pfb-price-type-select', function() {
            const $row = $(this).closest('tr');
            const variableId = $row.data('variable-id');
            const type = $(this).val();
            
            if (type === 'fixed') {
                $row.find('.pfb-price-sheet-value').html(`
                    <input type="number" name="variables[${variableId}][price_value]" class="pfb-form-control pfb-price-value-input" value="0" step="0.01">
                `);
            } else {
                $row.find('.pfb-price-sheet-value').html(`
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-ranges" data-variable-id="${variableId}">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Add ranges', 'price-form-builder'); ?>
                    </button>
                    <input type="hidden" name="variables[${variableId}][price_ranges]" class="pfb-price-ranges-input" value="[]">
                `);
            }
        });
        
        // Edit ranges
        $(document).on('click', '.pfb-edit-ranges', function() {
            currentVariableId = $(this).data('variable-id');
            
            // Get ranges
            const $input = $(this).siblings('.pfb-price-ranges-input');
            let ranges = [];
            
            try {
                ranges = JSON.parse($input.val());
            } catch (e) {
                ranges = [];
            }
            
            // Clear ranges list
            $('#pfb-ranges-list').empty();
            
            // Add ranges
            if (ranges.length > 0) {
                ranges.forEach(function(range, index) {
                    addRangeRow(range.min, range.max, range.price);
                });
            } else {
                // Add one empty range
                addRangeRow('', '', '');
            }
            
            // Show modal
            $('#pfb-ranges-modal').show();
        });
        
        // Add range
        $('#pfb-add-range').on('click', function() {
            addRangeRow('', '', '');
        });
        
        // Remove range
        $(document).on('click', '.pfb-remove-range', function() {
            $(this).closest('.pfb-range-row').remove();
        });
        
        // Save ranges
        $('.pfb-save-ranges').on('click', function() {
            const ranges = [];
            
            // Collect ranges
            $('.pfb-range-row').each(function() {
                const min = $(this).find('.pfb-range-min').val();
                const max = $(this).find('.pfb-range-max').val();
                const price = $(this).find('.pfb-range-price').val();
                
                if (min !== '') {
                    ranges.push({
                        min: min,
                        max: max,
                        price: price
                    });
                }
            });
            
            // Update hidden input
            const $row = $(`.pfb-price-sheet-row[data-variable-id="${currentVariableId}"]`);
            $row.find('.pfb-price-ranges-input').val(JSON.stringify(ranges));
            
            // Update button text
            const $button = $row.find('.pfb-edit-ranges');
            if (ranges.length > 0) {
                $button.html(`<span class="dashicons dashicons-edit"></span> ${ranges.length} <?php _e('ranges', 'price-form-builder'); ?>`);
            } else {
                $button.html(`<span class="dashicons dashicons-plus"></span> <?php _e('Add ranges', 'price-form-builder'); ?>`);
            }
            
            // Close modal
            $('#pfb-ranges-modal').hide();
        });
        
        // Close modal
        $('.pfb-modal-close, .pfb-modal-cancel').on('click', function() {
            $('#pfb-ranges-modal').hide();
        });
        
        // Save price sheet
        $('#pfb-save-price-sheet').on('click', function() {
            const formData = $('#pfb-price-sheet-form').serialize();
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pfb_save_price_sheet',
                    nonce: pfb_data.nonce,
                    form_data: formData
                },
                beforeSend: function() {
                    $('#pfb-save-price-sheet').prop('disabled', true).html('<span class="dashicons dashicons-update pfb-spin"></span> <?php _e('Saving...', 'price-form-builder'); ?>');
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        const $notification = $('<div class="pfb-notification pfb-notification-success">' + response.data.message + '</div>');
                        $('.pfb-admin-content').prepend($notification);
                        
                        setTimeout(function() {
                            $notification.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 3000);
                        
                        // Reload page after a delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        // Show error message
                        const $notification = $('<div class="pfb-notification pfb-notification-error">' + response.data.message + '</div>');
                        $('.pfb-admin-content').prepend($notification);
                        
                        setTimeout(function() {
                            $notification.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 3000);
                    }
                },
                error: function() {
                    // Show error message
                    const $notification = $('<div class="pfb-notification pfb-notification-error"><?php _e('An error occurred while saving the price sheet.', 'price-form-builder'); ?></div>');
                    $('.pfb-admin-content').prepend($notification);
                    
                    setTimeout(function() {
                        $notification.fadeOut(function() {
                            $(this).remove();
                        });
                    }, 3000);
                },
                complete: function() {
                    $('#pfb-save-price-sheet').prop('disabled', false).html('<span class="dashicons dashicons-saved"></span> <?php _e('Save Changes', 'price-form-builder'); ?>');
                }
            });
        });
        
        // Search variables
        $('#pfb-price-sheet-search').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            $('.pfb-price-sheet-row').each(function() {
                const name = $(this).find('.pfb-price-sheet-name input').val().toLowerCase();
                const key = $(this).find('.pfb-price-sheet-key input').val().toLowerCase();
                
                if (name.includes(searchTerm) || key.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
        
        // Helper function to add a range row
        function addRangeRow(min, max, price) {
            const $row = $(`
                <div class="pfb-range-row">
                    <input type="number" class="pfb-form-control pfb-range-min" placeholder="<?php _e('Min', 'price-form-builder'); ?>" value="${min}">
                    <input type="number" class="pfb-form-control pfb-range-max" placeholder="<?php _e('Max (optional)', 'price-form-builder'); ?>" value="${max}">
                    <input type="number" class="pfb-form-control pfb-range-price" placeholder="<?php _e('Price', 'price-form-builder'); ?>" value="${price}" step="0.01">
                    <button type="button" class="pfb-btn pfb-btn-danger pfb-remove-range">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            `);
            
            $('#pfb-ranges-list').append($row);
        }
    });
</script>

<style>
    .pfb-price-sheet-table-container {
        overflow-x: auto;
        margin-top: 20px;
    }
    
    .pfb-price-sheet-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #c3c4c7;
        background: #fff;
    }
    
    .pfb-price-sheet-table th,
    .pfb-price-sheet-table td {
        padding: 8px;
        border: 1px solid #c3c4c7;
        vertical-align: middle;
    }
    
    .pfb-price-sheet-table th {
        background: #f6f7f7;
        font-weight: 600;
        text-align: left;
    }
    
    .pfb-price-sheet-name {
        width: 25%;
    }
    
    .pfb-price-sheet-key {
        width: 20%;
    }
    
    .pfb-price-sheet-type {
        width: 15%;
    }
    
    .pfb-price-sheet-value {
        width: 25%;
    }
    
    .pfb-price-sheet-actions {
        width: 15%;
        text-align: center;
    }
    
    .pfb-price-sheet-empty-row td {
        text-align: center;
        padding: 20px;
        color: #646970;
    }
    
    .pfb-price-sheet-toolbar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    
    .pfb-ranges-header {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        font-weight: 600;
    }
    
    .pfb-range-min-header,
    .pfb-range-max-header,
    .pfb-range-price-header {
        flex: 1;
    }
    
    .pfb-range-action-header {
        width: 40px;
    }
    
    .pfb-range-row {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        align-items: center;
    }
    
    .pfb-range-min,
    .pfb-range-max,
    .pfb-range-price {
        flex: 1;
    }
    
    #pfb-add-range {
        margin-top: 10px;
    }
    
    .pfb-spin {
        animation: pfb-spin 2s linear infinite;
    }
    
    @keyframes pfb-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
