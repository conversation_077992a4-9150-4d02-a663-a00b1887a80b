<?php
/**
 * Admin forms list template.
 *
 * @since      1.0.0
 */
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1><?php _e('Price Forms', 'price-form-builder'); ?></h1>
        <div class="pfb-admin-actions">
            <a href="<?php echo admin_url('admin.php?page=pfb-form-editor'); ?>" class="pfb-btn pfb-btn-primary">
                <span class="dashicons dashicons-plus"></span> <?php _e('Add New Form', 'price-form-builder'); ?>
            </a>
        </div>
    </div>
    
    <div class="pfb-admin-content">
        <?php
        // Get forms
        $forms = PFB_DB::get_forms();
        ?>
        
        <?php if (empty($forms)) : ?>
            <div class="pfb-empty-message">
                <p><?php _e('No forms found. Click the "Add New Form" button to create your first form.', 'price-form-builder'); ?></p>
            </div>
        <?php else : ?>
            <table class="pfb-table">
                <thead>
                    <tr>
                        <th><?php _e('ID', 'price-form-builder'); ?></th>
                        <th><?php _e('Title', 'price-form-builder'); ?></th>
                        <th><?php _e('Status', 'price-form-builder'); ?></th>
                        <th><?php _e('Shortcode', 'price-form-builder'); ?></th>
                        <th><?php _e('Created', 'price-form-builder'); ?></th>
                        <th><?php _e('Actions', 'price-form-builder'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($forms as $form) : ?>
                        <tr>
                            <td><?php echo esc_html($form['id']); ?></td>
                            <td><?php echo esc_html($form['title']); ?></td>
                            <td>
                                <?php if ($form['status'] === 'publish') : ?>
                                    <span class="pfb-status pfb-status-published"><?php _e('Published', 'price-form-builder'); ?></span>
                                <?php else : ?>
                                    <span class="pfb-status pfb-status-draft"><?php _e('Draft', 'price-form-builder'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="pfb-shortcode-preview">
                                    <code>[price_form id="<?php echo esc_attr($form['id']); ?>"]</code>
                                    <button type="button" class="pfb-copy-shortcode pfb-btn pfb-btn-secondary" data-clipboard-text="[price_form id=&quot;<?php echo esc_attr($form['id']); ?>&quot;]">
                                        <span class="dashicons dashicons-clipboard"></span>
                                    </button>
                                </div>
                            </td>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($form['created_at'])); ?></td>
                            <td class="pfb-actions">
                                <a href="<?php echo admin_url('admin.php?page=pfb-form-editor&form_id=' . $form['id']); ?>" class="pfb-btn pfb-btn-secondary">
                                    <span class="dashicons dashicons-edit"></span> <?php _e('Edit', 'price-form-builder'); ?>
                                </a>
                                <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-form" data-form-id="<?php echo esc_attr($form['id']); ?>">
                                    <span class="dashicons dashicons-trash"></span> <?php _e('Delete', 'price-form-builder'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Copy shortcode
        $('.pfb-copy-shortcode').on('click', function() {
            const text = $(this).data('clipboard-text');
            const $temp = $('<input>');
            $('body').append($temp);
            $temp.val(text).select();
            document.execCommand('copy');
            $temp.remove();
            
            $(this).text('Copied!');
            setTimeout(() => {
                $(this).html('<span class="dashicons dashicons-clipboard"></span>');
            }, 2000);
        });
        
        // Delete form
        $('.pfb-delete-form').on('click', function() {
            const formId = $(this).data('form-id');
            
            if (confirm(pfb_data.i18n.confirm_delete)) {
                $.ajax({
                    url: pfb_data.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'pfb_delete_form',
                        nonce: pfb_data.nonce,
                        form_id: formId
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data.message);
                        }
                    }
                });
            }
        });
    });
</script>
