<?php
/**
 * Admin settings template.
 *
 * @since      1.0.0
 */
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1><?php _e('Settings', 'price-form-builder'); ?></h1>
        <div class="pfb-admin-actions">
            <button id="pfb-save-settings" class="pfb-btn pfb-btn-primary">
                <span class="dashicons dashicons-saved"></span> <?php _e('Save Settings', 'price-form-builder'); ?>
            </button>
        </div>
    </div>

    <div class="pfb-admin-content">
        <div class="pfb-tabs">
            <div class="pfb-tab active" data-tab="pfb-tab-general"><?php _e('General', 'price-form-builder'); ?></div>
            <div class="pfb-tab" data-tab="pfb-tab-display"><?php _e('Display', 'price-form-builder'); ?></div>
            <div class="pfb-tab" data-tab="pfb-tab-languages"><?php _e('Languages', 'price-form-builder'); ?></div>
            <div class="pfb-tab" data-tab="pfb-tab-formula-test"><?php _e('Formula Test', 'price-form-builder'); ?></div>
        </div>

        <form id="pfb-settings-form">
            <div id="pfb-tab-general" class="pfb-tab-content active">
                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('General Settings', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <label for="pfb_default_currency"><?php _e('Default Currency', 'price-form-builder'); ?></label>
                        <select id="pfb_default_currency" class="pfb-form-control" name="pfb_default_currency">
                            <?php
                            $currencies = PFB_Currency::get_all_currencies();
                            $default_currency = PFB_Currency::get_default_currency();

                            foreach ($currencies as $currency) {
                                $selected = ($currency['is_default'] == 1) ? 'selected' : '';
                                echo '<option value="' . esc_attr($currency['id']) . '" ' . $selected . '>' . esc_html($currency['name'] . ' (' . $currency['code'] . ')') . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_show_currency_selector"><?php _e('Show Currency Selector', 'price-form-builder'); ?></label>
                        <select id="pfb_show_currency_selector" class="pfb-form-control" name="pfb_show_currency_selector">
                            <option value="1" selected><?php _e('Yes', 'price-form-builder'); ?></option>
                            <option value="0"><?php _e('No', 'price-form-builder'); ?></option>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_default_form_status"><?php _e('Default Form Status', 'price-form-builder'); ?></label>
                        <select id="pfb_default_form_status" class="pfb-form-control" name="pfb_default_form_status">
                            <option value="publish" selected><?php _e('Published', 'price-form-builder'); ?></option>
                            <option value="draft"><?php _e('Draft', 'price-form-builder'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Form Defaults', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <label for="pfb_default_submit_text"><?php _e('Default Submit Button Text', 'price-form-builder'); ?></label>
                        <input type="text" id="pfb_default_submit_text" class="pfb-form-control" name="pfb_default_submit_text" value="<?php _e('Calculate Price', 'price-form-builder'); ?>">
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_default_success_message"><?php _e('Default Success Message', 'price-form-builder'); ?></label>
                        <textarea id="pfb_default_success_message" class="pfb-form-control" name="pfb_default_success_message" rows="3"><?php _e('Thank you for your submission!', 'price-form-builder'); ?></textarea>
                    </div>
                </div>
            </div>

            <div id="pfb-tab-display" class="pfb-tab-content">
                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Form Display', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <label for="pfb_form_theme"><?php _e('Form Theme', 'price-form-builder'); ?></label>
                        <select id="pfb_form_theme" class="pfb-form-control" name="pfb_form_theme">
                            <option value="default" selected><?php _e('Default', 'price-form-builder'); ?></option>
                            <option value="light"><?php _e('Light', 'price-form-builder'); ?></option>
                            <option value="dark"><?php _e('Dark', 'price-form-builder'); ?></option>
                            <option value="minimal"><?php _e('Minimal', 'price-form-builder'); ?></option>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_primary_color"><?php _e('Primary Color', 'price-form-builder'); ?></label>
                        <input type="color" id="pfb_primary_color" class="pfb-form-control" name="pfb_primary_color" value="#2575fc">
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_secondary_color"><?php _e('Secondary Color', 'price-form-builder'); ?></label>
                        <input type="color" id="pfb_secondary_color" class="pfb-form-control" name="pfb_secondary_color" value="#6a11cb">
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_font_family"><?php _e('Font Family', 'price-form-builder'); ?></label>
                        <select id="pfb_font_family" class="pfb-form-control" name="pfb_font_family">
                            <option value="Poppins" selected>Poppins</option>
                            <option value="Roboto">Roboto</option>
                            <option value="Open Sans">Open Sans</option>
                            <option value="Lato">Lato</option>
                            <option value="Montserrat">Montserrat</option>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_border_radius"><?php _e('Border Radius', 'price-form-builder'); ?></label>
                        <input type="range" id="pfb_border_radius" class="pfb-form-control" name="pfb_border_radius" min="0" max="20" value="5">
                        <div class="pfb-range-value"><span id="pfb_border_radius_value">5</span>px</div>
                    </div>
                </div>

                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Animation', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <label for="pfb_enable_animations"><?php _e('Enable Animations', 'price-form-builder'); ?></label>
                        <select id="pfb_enable_animations" class="pfb-form-control" name="pfb_enable_animations">
                            <option value="1" selected><?php _e('Yes', 'price-form-builder'); ?></option>
                            <option value="0"><?php _e('No', 'price-form-builder'); ?></option>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_animation_speed"><?php _e('Animation Speed', 'price-form-builder'); ?></label>
                        <select id="pfb_animation_speed" class="pfb-form-control" name="pfb_animation_speed">
                            <option value="fast"><?php _e('Fast', 'price-form-builder'); ?></option>
                            <option value="normal" selected><?php _e('Normal', 'price-form-builder'); ?></option>
                            <option value="slow"><?php _e('Slow', 'price-form-builder'); ?></option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="pfb-tab-languages" class="pfb-tab-content">
                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Language Settings', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <label for="pfb_default_language"><?php _e('Default Language', 'price-form-builder'); ?></label>
                        <select id="pfb_default_language" class="pfb-form-control" name="pfb_default_language">
                            <option value="en_US" selected><?php _e('English (United States)', 'price-form-builder'); ?></option>
                            <option value="es_ES"><?php _e('Spanish (Spain)', 'price-form-builder'); ?></option>
                            <option value="fr_FR"><?php _e('French (France)', 'price-form-builder'); ?></option>
                            <option value="de_DE"><?php _e('German (Germany)', 'price-form-builder'); ?></option>
                            <option value="it_IT"><?php _e('Italian (Italy)', 'price-form-builder'); ?></option>
                            <option value="ja"><?php _e('Japanese', 'price-form-builder'); ?></option>
                            <option value="zh_CN"><?php _e('Chinese (China)', 'price-form-builder'); ?></option>
                            <option value="ar"><?php _e('Arabic', 'price-form-builder'); ?></option>
                        </select>
                    </div>

                    <div class="pfb-form-group">
                        <label for="pfb_enable_language_switcher"><?php _e('Enable Language Switcher', 'price-form-builder'); ?></label>
                        <select id="pfb_enable_language_switcher" class="pfb-form-control" name="pfb_enable_language_switcher">
                            <option value="1" selected><?php _e('Yes', 'price-form-builder'); ?></option>
                            <option value="0"><?php _e('No', 'price-form-builder'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Available Languages', 'price-form-builder'); ?></h3>

                    <div class="pfb-form-group">
                        <div class="pfb-language-list">
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_en_US" name="pfb_languages[]" value="en_US" checked disabled>
                                <label for="pfb_lang_en_US"><?php _e('English (United States)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_es_ES" name="pfb_languages[]" value="es_ES" checked>
                                <label for="pfb_lang_es_ES"><?php _e('Spanish (Spain)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_fr_FR" name="pfb_languages[]" value="fr_FR" checked>
                                <label for="pfb_lang_fr_FR"><?php _e('French (France)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_de_DE" name="pfb_languages[]" value="de_DE" checked>
                                <label for="pfb_lang_de_DE"><?php _e('German (Germany)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_it_IT" name="pfb_languages[]" value="it_IT">
                                <label for="pfb_lang_it_IT"><?php _e('Italian (Italy)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_ja" name="pfb_languages[]" value="ja">
                                <label for="pfb_lang_ja"><?php _e('Japanese', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_zh_CN" name="pfb_languages[]" value="zh_CN">
                                <label for="pfb_lang_zh_CN"><?php _e('Chinese (China)', 'price-form-builder'); ?></label>
                            </div>
                            <div class="pfb-language-item">
                                <input type="checkbox" id="pfb_lang_ar" name="pfb_languages[]" value="ar">
                                <label for="pfb_lang_ar"><?php _e('Arabic', 'price-form-builder'); ?></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="pfb-tab-formula-test" class="pfb-tab-content">
                <div class="pfb-settings-section">
                    <h3 class="pfb-settings-section-title"><?php _e('Formula Test Tool', 'price-form-builder'); ?></h3>
                    <p><?php _e('Use this tool to test complex formulas with nested functions.', 'price-form-builder'); ?></p>

                    <div class="pfb-form-group">
                        <label for="pfb_test_formula"><?php _e('Formula to Test', 'price-form-builder'); ?></label>
                        <input type="text" id="pfb_test_formula" class="pfb-form-control" value="(floor(max(0,{X}-5000)/1000))*{Z}" style="width: 100%;">
                        <p class="pfb-form-help"><?php _e('Enter a formula using {X}, {Y}, {Z} as variables.', 'price-form-builder'); ?></p>
                    </div>

                    <div class="pfb-form-row" style="display: flex; gap: 10px;">
                        <div class="pfb-form-group" style="flex: 1;">
                            <label for="pfb_test_x"><?php _e('X Value', 'price-form-builder'); ?></label>
                            <input type="number" id="pfb_test_x" class="pfb-form-control" value="6000">
                        </div>
                        <div class="pfb-form-group" style="flex: 1;">
                            <label for="pfb_test_y"><?php _e('Y Value', 'price-form-builder'); ?></label>
                            <input type="number" id="pfb_test_y" class="pfb-form-control" value="1000">
                        </div>
                        <div class="pfb-form-group" style="flex: 1;">
                            <label for="pfb_test_z"><?php _e('Z Value', 'price-form-builder'); ?></label>
                            <input type="number" id="pfb_test_z" class="pfb-form-control" value="2">
                        </div>
                    </div>

                    <div class="pfb-form-group">
                        <button type="button" id="pfb_test_formula_btn" class="pfb-btn pfb-btn-primary">
                            <?php _e('Test Formula', 'price-form-builder'); ?>
                        </button>
                    </div>

                    <div id="pfb_formula_result" class="pfb-result-box" style="margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; display: none;">
                        <h4><?php _e('Test Results', 'price-form-builder'); ?></h4>
                        <div class="pfb-result-content"></div>
                    </div>

                    <div class="pfb-settings-section" style="margin-top: 30px;">
                        <h3 class="pfb-settings-section-title"><?php _e('Common Formula Patterns', 'price-form-builder'); ?></h3>
                        <ul>
                            <li><strong>max(0, X-Y)</strong> - <?php _e('Returns X-Y if greater than 0, otherwise returns 0', 'price-form-builder'); ?></li>
                            <li><strong>floor(X/Y)</strong> - <?php _e('Divides X by Y and rounds down to the nearest integer', 'price-form-builder'); ?></li>
                            <li><strong>ceil(X/Y)</strong> - <?php _e('Divides X by Y and rounds up to the nearest integer', 'price-form-builder'); ?></li>
                            <li><strong>(floor(max(0,X-Y)/Z))*W</strong> - <?php _e('Complex nested formula example', 'price-form-builder'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="pfb-admin-footer">
        <button id="pfb-save-settings-bottom" class="pfb-btn pfb-btn-primary"><?php _e('Save Settings', 'price-form-builder'); ?></button>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Update border radius value display
        $('#pfb_border_radius').on('input', function() {
            $('#pfb_border_radius_value').text($(this).val());
        });

        // Save settings
        $('#pfb-save-settings, #pfb-save-settings-bottom').on('click', function() {
            const formData = $('#pfb-settings-form').serialize();

            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_save_settings',
                    nonce: pfb_data.nonce,
                    form_data: formData
                },
                beforeSend: function() {
                    $(this).prop('disabled', true).text(pfb_data.i18n.loading);
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        const $notification = $('<div class="pfb-notification pfb-notification-success">' + response.data.message + '</div>');
                        $('.pfb-admin-content').prepend($notification);

                        setTimeout(function() {
                            $notification.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 3000);
                    } else {
                        // Show error message
                        const $notification = $('<div class="pfb-notification pfb-notification-error">' + response.data.message + '</div>');
                        $('.pfb-admin-content').prepend($notification);

                        setTimeout(function() {
                            $notification.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 3000);
                    }
                },
                complete: function() {
                    $(this).prop('disabled', false).text(pfb_data.i18n.save);
                }
            });
        });

        // Formula testing functionality
        $('#pfb_test_formula_btn').on('click', function() {
            const formula = $('#pfb_test_formula').val();
            const xValue = parseFloat($('#pfb_test_x').val());
            const yValue = parseFloat($('#pfb_test_y').val());
            const zValue = parseFloat($('#pfb_test_z').val());

            // Validate inputs
            if (!formula) {
                alert('Please enter a formula to test.');
                return;
            }

            // Show loading state
            $(this).prop('disabled', true).text('Testing...');
            $('#pfb_formula_result').hide();

            // Test with both calculators
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_test_formula',
                    nonce: pfb_data.nonce,
                    formula: formula,
                    x_value: xValue,
                    y_value: yValue,
                    z_value: zValue
                },
                success: function(response) {
                    if (response.success) {
                        // Show results
                        $('#pfb_formula_result').show();

                        let resultHtml = '<table class="wp-list-table widefat fixed striped">';
                        resultHtml += '<thead><tr>';
                        resultHtml += '<th>Calculator</th>';
                        resultHtml += '<th>Result</th>';
                        resultHtml += '<th>Manual Calculation</th>';
                        resultHtml += '</tr></thead><tbody>';

                        // Simple Calculator result
                        resultHtml += '<tr>';
                        resultHtml += '<td>Simple Calculator</td>';
                        resultHtml += '<td>' + response.data.simple_result + '</td>';
                        resultHtml += '<td rowspan="3">' + response.data.manual_result + '</td>';
                        resultHtml += '</tr>';

                        // Formula Parser result
                        resultHtml += '<tr>';
                        resultHtml += '<td>Formula Parser</td>';
                        resultHtml += '<td>' + response.data.parser_result + '</td>';
                        resultHtml += '</tr>';

                        // Modified Formula result
                        resultHtml += '<tr>';
                        resultHtml += '<td>Modified Formula</td>';
                        resultHtml += '<td>' + response.data.modified_result + '</td>';
                        resultHtml += '</tr>';

                        resultHtml += '</tbody></table>';

                        // Add debug info
                        resultHtml += '<h4 style="margin-top: 20px;">Debug Information</h4>';
                        resultHtml += '<pre style="background: #f0f0f0; padding: 10px; overflow: auto; max-height: 200px;">';
                        resultHtml += response.data.debug_info;
                        resultHtml += '</pre>';

                        $('#pfb_formula_result .pfb-result-content').html(resultHtml);
                    } else {
                        // Show error
                        $('#pfb_formula_result').show();
                        $('#pfb_formula_result .pfb-result-content').html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                    }
                },
                error: function() {
                    $('#pfb_formula_result').show();
                    $('#pfb_formula_result .pfb-result-content').html('<div class="notice notice-error"><p>An error occurred while testing the formula.</p></div>');
                },
                complete: function() {
                    $('#pfb_test_formula_btn').prop('disabled', false).text('Test Formula');
                }
            });
        });
    });
</script>
