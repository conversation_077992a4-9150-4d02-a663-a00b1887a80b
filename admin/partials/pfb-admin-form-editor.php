<?php
/**
 * Admin form editor template.
 *
 * @since      1.0.0
 */
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1><?php echo isset($_GET['form_id']) ? __('Edit Form', 'price-form-builder') : __('Add New Form', 'price-form-builder'); ?></h1>
        <div class="pfb-admin-actions">
            <button id="pfb-save-form" class="pfb-btn pfb-btn-primary"><?php _e('Save Form', 'price-form-builder'); ?></button>
            <a href="<?php echo admin_url('admin.php?page=pfb-forms'); ?>" class="pfb-btn pfb-btn-secondary"><?php _e('Cancel', 'price-form-builder'); ?></a>
        </div>
    </div>

    <div class="pfb-admin-content">
        <form id="pfb-form-editor-form">
            <div class="pfb-form-group">
                <label for="form_title"><?php _e('Form Title', 'price-form-builder'); ?></label>
                <input type="text" id="form_title" class="pfb-form-control" placeholder="<?php _e('Enter form title', 'price-form-builder'); ?>" required>
            </div>

            <div class="pfb-form-group">
                <label for="form_description"><?php _e('Form Description', 'price-form-builder'); ?></label>
                <textarea id="form_description" class="pfb-form-control" rows="3" placeholder="<?php _e('Enter form description (optional)', 'price-form-builder'); ?>"></textarea>
            </div>

            <div class="pfb-form-group">
                <label for="form_status"><?php _e('Form Status', 'price-form-builder'); ?></label>
                <select id="form_status" class="pfb-form-control">
                    <option value="publish"><?php _e('Published', 'price-form-builder'); ?></option>
                    <option value="draft"><?php _e('Draft', 'price-form-builder'); ?></option>
                </select>
            </div>

            <div class="pfb-tabs">
                <div class="pfb-tab active" data-tab="pfb-tab-fields"><?php _e('Form Fields', 'price-form-builder'); ?></div>
                <div class="pfb-tab" data-tab="pfb-tab-settings"><?php _e('Form Settings', 'price-form-builder'); ?></div>
                <div class="pfb-tab" data-tab="pfb-tab-translations"><?php _e('Translations', 'price-form-builder'); ?></div>
            </div>

            <div id="pfb-tab-fields" class="pfb-tab-content active">
                <div id="pfb-form-builder" class="pfb-builder">
                    <div class="pfb-builder-sidebar">
                        <h3><?php _e('Add Field', 'price-form-builder'); ?></h3>
                        <div class="pfb-field-types">
                            <?php foreach (PFB_Form::get_field_types() as $type => $label) : ?>
                                <button type="button" class="pfb-field-type pfb-add-field-btn" data-type="<?php echo esc_attr($type); ?>">
                                    <?php
                                    $icon = 'menu';
                                    switch ($type) {
                                        case 'text': $icon = 'editor-textcolor'; break;
                                        case 'number': $icon = 'calculator'; break;
                                        case 'dropdown': $icon = 'arrow-down-alt2'; break;
                                        case 'radio': $icon = 'marker'; break;
                                        case 'checkbox': $icon = 'yes'; break;
                                        case 'total': $icon = 'money-alt'; break;
                                    }
                                    ?>
                                    <span class="dashicons dashicons-<?php echo $icon; ?>"></span>
                                    <?php echo esc_html($label); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>

                        <h3><?php _e('Instructions', 'price-form-builder'); ?></h3>
                        <p><?php _e('Click on a field type button to add it to your form.', 'price-form-builder'); ?></p>
                        <p><?php _e('Click on a field to edit its properties.', 'price-form-builder'); ?></p>
                        <p><?php _e('Use the up/down buttons to reorder fields.', 'price-form-builder'); ?></p>

                        <h3><?php _e('Shortcode', 'price-form-builder'); ?></h3>
                        <?php if (isset($_GET['form_id'])) : ?>
                            <div class="pfb-shortcode-preview">
                                <code>[price_form id="<?php echo intval($_GET['form_id']); ?>"]</code>
                                <button type="button" class="pfb-copy-shortcode pfb-btn pfb-btn-secondary" data-clipboard-text="[price_form id=&quot;<?php echo intval($_GET['form_id']); ?>&quot;]">
                                    <span class="dashicons dashicons-clipboard"></span>
                                </button>
                            </div>
                        <?php else : ?>
                            <p><?php _e('Save the form to get the shortcode.', 'price-form-builder'); ?></p>
                        <?php endif; ?>
                    </div>

                    <div class="pfb-builder-content">
                        <div class="pfb-form-fields-container">
                            <div class="pfb-form-fields-header">
                                <h3><?php _e('Form Fields', 'price-form-builder'); ?></h3>
                                <div class="pfb-form-fields-actions">
                                    <button type="button" id="pfb-collapse-all-fields" class="pfb-btn pfb-btn-secondary">
                                        <span class="dashicons dashicons-arrow-up-alt2"></span> <?php _e('Collapse All', 'price-form-builder'); ?>
                                    </button>
                                    <button type="button" id="pfb-expand-all-fields" class="pfb-btn pfb-btn-secondary">
                                        <span class="dashicons dashicons-arrow-down-alt2"></span> <?php _e('Expand All', 'price-form-builder'); ?>
                                    </button>
                                </div>
                            </div>
                            <div class="pfb-form-fields">
                                <!-- Fields will be added here -->
                            </div>

                            <div class="pfb-empty-form-message">
                                <p><?php _e('Click on a field type button on the left to add fields to your form.', 'price-form-builder'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="pfb-tab-settings" class="pfb-tab-content">
                <div class="pfb-form-group">
                    <label for="form_template"><?php _e('Form Template', 'price-form-builder'); ?></label>
                    <select id="form_template" class="pfb-form-control">
                        <option value="default"><?php _e('Default (Modern)', 'price-form-builder'); ?></option>
                        <option value="minimal"><?php _e('Minimal', 'price-form-builder'); ?></option>
                        <option value="professional"><?php _e('Professional', 'price-form-builder'); ?></option>
                    </select>
                    <div class="pfb-form-help"><?php _e('Select a template for the form appearance.', 'price-form-builder'); ?></div>
                </div>

                <div class="pfb-form-group">
                    <label for="form_currency"><?php _e('Default Currency', 'price-form-builder'); ?></label>
                    <select id="form_currency" class="pfb-form-control">
                        <?php
                        $currencies = PFB_Currency::get_all_currencies();
                        $default_currency = PFB_Currency::get_default_currency();

                        foreach ($currencies as $currency) {
                            $selected = ($currency['is_default'] == 1) ? 'selected' : '';
                            echo '<option value="' . esc_attr($currency['code']) . '" ' . $selected . '>' . esc_html($currency['name'] . ' (' . $currency['code'] . ')') . '</option>';
                        }
                        ?>
                    </select>
                </div>

                <div class="pfb-form-group">
                    <label for="form_show_currency_selector"><?php _e('Show Currency Selector', 'price-form-builder'); ?></label>
                    <select id="form_show_currency_selector" class="pfb-form-control">
                        <option value="1"><?php _e('Yes', 'price-form-builder'); ?></option>
                        <option value="0"><?php _e('No', 'price-form-builder'); ?></option>
                    </select>
                </div>

                <div class="pfb-form-group">
                    <label for="form_submit_button_text"><?php _e('Submit Button Text', 'price-form-builder'); ?></label>
                    <input type="text" id="form_submit_button_text" class="pfb-form-control" value="<?php _e('Calculate Price', 'price-form-builder'); ?>">
                </div>

                <div class="pfb-form-group">
                    <label for="form_success_message"><?php _e('Success Message', 'price-form-builder'); ?></label>
                    <textarea id="form_success_message" class="pfb-form-control" rows="3"><?php _e('Thank you for your submission!', 'price-form-builder'); ?></textarea>
                </div>
            </div>

            <div id="pfb-tab-translations" class="pfb-tab-content">
                <div class="pfb-form-group">
                    <label for="form_languages"><?php _e('Available Languages', 'price-form-builder'); ?></label>
                    <select id="form_languages" class="pfb-form-control">
                        <option value="en_US"><?php _e('English (United States)', 'price-form-builder'); ?></option>
                        <option value="es_ES"><?php _e('Spanish (Spain)', 'price-form-builder'); ?></option>
                        <option value="fr_FR"><?php _e('French (France)', 'price-form-builder'); ?></option>
                        <option value="de_DE"><?php _e('German (Germany)', 'price-form-builder'); ?></option>
                        <option value="it_IT"><?php _e('Italian (Italy)', 'price-form-builder'); ?></option>
                        <option value="ja"><?php _e('Japanese', 'price-form-builder'); ?></option>
                        <option value="zh_CN"><?php _e('Chinese (China)', 'price-form-builder'); ?></option>
                        <option value="ar"><?php _e('Arabic', 'price-form-builder'); ?></option>
                    </select>
                    <div class="pfb-form-help"><?php _e('Select a language to add or edit translations.', 'price-form-builder'); ?></div>
                </div>

                <div id="pfb-translations-container">
                    <div class="pfb-translation-fields">
                        <div class="pfb-form-group">
                            <label for="translation_title"><?php _e('Form Title', 'price-form-builder'); ?></label>
                            <input type="text" id="translation_title" class="pfb-form-control" placeholder="<?php _e('Translated title', 'price-form-builder'); ?>">
                        </div>

                        <div class="pfb-form-group">
                            <label for="translation_description"><?php _e('Form Description', 'price-form-builder'); ?></label>
                            <textarea id="translation_description" class="pfb-form-control" rows="3" placeholder="<?php _e('Translated description', 'price-form-builder'); ?>"></textarea>
                        </div>

                        <div class="pfb-form-group">
                            <label for="translation_submit_button"><?php _e('Submit Button Text', 'price-form-builder'); ?></label>
                            <input type="text" id="translation_submit_button" class="pfb-form-control" placeholder="<?php _e('Translated button text', 'price-form-builder'); ?>">
                        </div>

                        <div class="pfb-form-group">
                            <label for="translation_success_message"><?php _e('Success Message', 'price-form-builder'); ?></label>
                            <textarea id="translation_success_message" class="pfb-form-control" rows="3" placeholder="<?php _e('Translated success message', 'price-form-builder'); ?>"></textarea>
                        </div>

                        <div id="pfb-field-translations">
                            <!-- Field translations will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="pfb-admin-footer">
        <button id="pfb-save-form-bottom" class="pfb-btn pfb-btn-primary"><?php _e('Save Form', 'price-form-builder'); ?></button>
    </div>
</div>

<script>
    // Initialize clipboard.js for copying shortcode
    jQuery(document).ready(function($) {
        $('.pfb-copy-shortcode').on('click', function() {
            const text = $(this).data('clipboard-text');
            const $temp = $('<input>');
            $('body').append($temp);
            $temp.val(text).select();
            document.execCommand('copy');
            $temp.remove();

            $(this).text('Copied!');
            setTimeout(() => {
                $(this).html('<span class="dashicons dashicons-clipboard"></span>');
            }, 2000);
        });

        // Connect bottom save button to top save button
        $('#pfb-save-form-bottom').on('click', function(e) {
            e.preventDefault();
            console.log('PFB Admin: Bottom save button clicked!');
            $('#pfb-save-form').trigger('click');
        });
    });
</script>

<!-- Modal for selecting dynamic values -->
<div id="pfb-dynamic-value-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2><?php _e('Select Dynamic Value', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <div class="pfb-dynamic-value-search">
                <input type="text" id="pfb-dynamic-value-search-input" class="pfb-form-control" placeholder="<?php _e('Search variables...', 'price-form-builder'); ?>">
            </div>
            <div class="pfb-dynamic-value-categories">
                <!-- Categories will be loaded dynamically -->
                <div class="pfb-dynamic-value-loading"><?php _e('Loading variables...', 'price-form-builder'); ?></div>
            </div>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>

<!-- Modal for selecting formula variables -->
<div id="pfb-formula-variables-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2><?php _e('Select Price Variable', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <div class="pfb-dynamic-value-search">
                <input type="text" id="pfb-formula-variables-search-input" class="pfb-form-control" placeholder="<?php _e('Search variables...', 'price-form-builder'); ?>">
            </div>
            <div class="pfb-formula-variables-list">
                <!-- Variables will be loaded dynamically -->
                <div class="pfb-formula-empty"><?php _e('Loading variables...', 'price-form-builder'); ?></div>
            </div>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>
