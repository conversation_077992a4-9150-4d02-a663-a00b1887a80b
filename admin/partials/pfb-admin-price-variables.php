<?php
/**
 * Admin price variables template.
 *
 * @since      1.0.0
 */
?>

<div class="wrap pfb-admin-wrap">
    <div class="pfb-admin-header">
        <h1><?php _e('Price Variables', 'price-form-builder'); ?></h1>
        <div class="pfb-admin-actions">
            <button id="pfb-add-variable" class="pfb-btn pfb-btn-primary">
                <span class="dashicons dashicons-plus"></span> <?php _e('Add New Variable', 'price-form-builder'); ?>
            </button>
        </div>
    </div>

    <div class="pfb-admin-content">
        <div class="pfb-tabs">
            <div class="pfb-tab active" data-tab="pfb-tab-variables"><?php _e('Variables', 'price-form-builder'); ?></div>
            <div class="pfb-tab" data-tab="pfb-tab-categories"><?php _e('Categories', 'price-form-builder'); ?></div>
        </div>

        <div id="pfb-tab-variables" class="pfb-tab-content active">
            <div id="pfb-variables-container">
                <?php
                // Get categories
                $categories = PFB_DB::get_price_categories();

                if (empty($categories)) {
                    echo '<div class="pfb-empty-message">';
                    echo '<p>' . __('No categories found. Create a category first to add variables.', 'price-form-builder') . '</p>';
                    echo '</div>';
                } else {
                    foreach ($categories as $category) {
                        // Get variables for this category
                        $variables = PFB_DB::get_price_variables(array('category_id' => $category['id']));
                        ?>
                        <div class="pfb-variable-category" data-category-id="<?php echo esc_attr($category['id']); ?>">
                            <div class="pfb-variable-category-header">
                                <span class="pfb-variable-category-name"><?php echo esc_html($category['name']); ?></span>
                                <div class="pfb-variable-category-actions">
                                    <span class="pfb-variable-category-count"><?php echo count($variables); ?> <?php _e('variables', 'price-form-builder'); ?></span>
                                    <a href="<?php echo admin_url('admin.php?page=pfb-price-sheet&category_id=' . esc_attr($category['id'])); ?>" class="pfb-btn pfb-btn-secondary">
                                        <span class="dashicons dashicons-editor-table"></span> <?php _e('Edit as Sheet', 'price-form-builder'); ?>
                                    </a>
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-variable-to-category" data-category-id="<?php echo esc_attr($category['id']); ?>">
                                        <span class="dashicons dashicons-plus"></span> <?php _e('Add Variable', 'price-form-builder'); ?>
                                    </button>
                                </div>
                            </div>

                            <?php if (empty($variables)) : ?>
                                <div class="pfb-empty-message">
                                    <p><?php _e('No variables in this category. Click "Add Variable" to create one.', 'price-form-builder'); ?></p>
                                </div>
                            <?php else : ?>
                                <div class="pfb-variable-table-container">
                                    <table class="pfb-variable-table">
                                        <thead>
                                            <tr>
                                                <th><?php _e('Name', 'price-form-builder'); ?></th>
                                                <th><?php _e('Variable Key', 'price-form-builder'); ?></th>
                                                <th><?php _e('Price Type', 'price-form-builder'); ?></th>
                                                <th><?php _e('Value', 'price-form-builder'); ?></th>
                                                <th><?php _e('Actions', 'price-form-builder'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($variables as $variable) : ?>
                                                <tr class="pfb-variable-row" data-variable-id="<?php echo esc_attr($variable['id']); ?>">
                                                    <td class="pfb-variable-name"><?php echo esc_html($variable['name']); ?></td>
                                                    <td class="pfb-variable-key"><code>{<?php echo esc_html($variable['variable_key']); ?>}</code></td>
                                                    <td class="pfb-variable-type">
                                                        <?php echo $variable['price_type'] === 'fixed' ? __('Fixed', 'price-form-builder') : __('Range', 'price-form-builder'); ?>
                                                    </td>
                                                    <td class="pfb-variable-value">
                                                        <?php if ($variable['price_type'] === 'fixed') : ?>
                                                            <?php echo esc_html(PFB_Currency::get_default_currency()['symbol'] . $variable['price_value']); ?>
                                                        <?php else : ?>
                                                            <?php
                                                            $ranges = maybe_unserialize($variable['price_ranges']);
                                                            if (!empty($ranges) && is_array($ranges)) {
                                                                echo '<span class="pfb-range-preview">' . count($ranges) . ' ' . __('ranges', 'price-form-builder') . '</span>';
                                                            } else {
                                                                echo '-';
                                                            }
                                                            ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="pfb-variable-actions">
                                                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-variable">
                                                            <span class="dashicons dashicons-edit"></span>
                                                        </button>
                                                        <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-variable">
                                                            <span class="dashicons dashicons-trash"></span>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>

        <div id="pfb-tab-categories" class="pfb-tab-content">
            <div id="pfb-categories-container">
                <div class="pfb-admin-actions">
                    <button id="pfb-add-category" class="pfb-btn pfb-btn-primary">
                        <span class="dashicons dashicons-plus"></span> <?php _e('Add New Category', 'price-form-builder'); ?>
                    </button>
                </div>

                <table class="pfb-table">
                    <thead>
                        <tr>
                            <th><?php _e('ID', 'price-form-builder'); ?></th>
                            <th><?php _e('Name', 'price-form-builder'); ?></th>
                            <th><?php _e('Description', 'price-form-builder'); ?></th>
                            <th><?php _e('Variables', 'price-form-builder'); ?></th>
                            <th><?php _e('Actions', 'price-form-builder'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($categories)) : ?>
                            <tr>
                                <td colspan="5"><?php _e('No categories found.', 'price-form-builder'); ?></td>
                            </tr>
                        <?php else : ?>
                            <?php foreach ($categories as $category) : ?>
                                <?php
                                // Get variables count for this category
                                $variables_count = count(PFB_DB::get_price_variables(array('category_id' => $category['id'])));
                                ?>
                                <tr data-category-id="<?php echo esc_attr($category['id']); ?>">
                                    <td><?php echo esc_html($category['id']); ?></td>
                                    <td><?php echo esc_html($category['name']); ?></td>
                                    <td><?php echo esc_html($category['description']); ?></td>
                                    <td><?php echo esc_html($variables_count); ?></td>
                                    <td class="pfb-actions">
                                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-edit-category">
                                            <span class="dashicons dashicons-edit"></span> <?php _e('Edit', 'price-form-builder'); ?>
                                        </button>
                                        <button type="button" class="pfb-btn pfb-btn-danger pfb-delete-category" <?php echo $variables_count > 0 ? 'disabled' : ''; ?>>
                                            <span class="dashicons dashicons-trash"></span> <?php _e('Delete', 'price-form-builder'); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Variable Modal -->
<div id="pfb-variable-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2 id="pfb-variable-modal-title"><?php _e('Add New Variable', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <form id="pfb-variable-form">
                <input type="hidden" id="variable_id" value="">

                <div class="pfb-form-group">
                    <label for="variable_category_id"><?php _e('Category', 'price-form-builder'); ?></label>
                    <select id="variable_category_id" class="pfb-form-control" required>
                        <?php foreach ($categories as $category) : ?>
                            <option value="<?php echo esc_attr($category['id']); ?>"><?php echo esc_html($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="pfb-form-group">
                    <label for="variable_name"><?php _e('Variable Name', 'price-form-builder'); ?></label>
                    <input type="text" id="variable_name" class="pfb-form-control" required>
                </div>

                <div class="pfb-form-group">
                    <label for="variable_key"><?php _e('Variable Key', 'price-form-builder'); ?></label>
                    <input type="text" id="variable_key" class="pfb-form-control" required>
                    <div class="pfb-form-help"><?php _e('Used in formulas as {variable_key}. Only lowercase letters, numbers, and underscores.', 'price-form-builder'); ?></div>
                </div>

                <div class="pfb-form-group">
                    <label for="variable_price_type"><?php _e('Price Type', 'price-form-builder'); ?></label>
                    <select id="variable_price_type" class="pfb-form-control">
                        <option value="fixed"><?php _e('Fixed Price', 'price-form-builder'); ?></option>
                        <option value="range"><?php _e('Range-based Price', 'price-form-builder'); ?></option>
                    </select>
                </div>

                <div id="variable_fixed_price_container" class="pfb-form-group">
                    <label for="variable_price_value"><?php _e('Price Value', 'price-form-builder'); ?></label>
                    <input type="number" id="variable_price_value" class="pfb-form-control" step="0.01">
                </div>

                <div id="variable_range_price_container" class="pfb-form-group" style="display: none;">
                    <label><?php _e('Price Ranges', 'price-form-builder'); ?></label>
                    <div id="variable_price_ranges">
                        <div class="pfb-price-range">
                            <input type="number" class="pfb-form-control pfb-range-min" placeholder="<?php _e('Min Quantity', 'price-form-builder'); ?>">
                            <input type="number" class="pfb-form-control pfb-range-max" placeholder="<?php _e('Max Quantity', 'price-form-builder'); ?>">
                            <input type="number" class="pfb-form-control pfb-range-price" placeholder="<?php _e('Price', 'price-form-builder'); ?>" step="0.01">
                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-range">+</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
            <button type="button" class="pfb-btn pfb-btn-primary pfb-modal-save"><?php _e('Save', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div id="pfb-category-modal" class="pfb-modal">
    <div class="pfb-modal-content">
        <div class="pfb-modal-header">
            <h2 id="pfb-category-modal-title"><?php _e('Add New Category', 'price-form-builder'); ?></h2>
            <span class="pfb-modal-close">&times;</span>
        </div>
        <div class="pfb-modal-body">
            <form id="pfb-category-form">
                <input type="hidden" id="category_id" value="">

                <div class="pfb-form-group">
                    <label for="category_name"><?php _e('Category Name', 'price-form-builder'); ?></label>
                    <input type="text" id="category_name" class="pfb-form-control" required>
                </div>

                <div class="pfb-form-group">
                    <label for="category_description"><?php _e('Description', 'price-form-builder'); ?></label>
                    <textarea id="category_description" class="pfb-form-control" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="pfb-modal-footer">
            <button type="button" class="pfb-btn pfb-btn-secondary pfb-modal-cancel"><?php _e('Cancel', 'price-form-builder'); ?></button>
            <button type="button" class="pfb-btn pfb-btn-primary pfb-modal-save"><?php _e('Save', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Variable price type toggle
        $('#variable_price_type').on('change', function() {
            if ($(this).val() === 'fixed') {
                $('#variable_fixed_price_container').show();
                $('#variable_range_price_container').hide();
            } else {
                $('#variable_fixed_price_container').hide();
                $('#variable_range_price_container').show();
            }
        });

        // Add price range
        $(document).on('click', '.pfb-add-range', function() {
            const $range = $(this).closest('.pfb-price-range');
            const $newRange = $range.clone();

            $newRange.find('input').val('');
            $newRange.find('.pfb-add-range').removeClass('pfb-add-range').addClass('pfb-remove-range').text('-');

            $('#variable_price_ranges').append($newRange);
        });

        // Remove price range
        $(document).on('click', '.pfb-remove-range', function() {
            $(this).closest('.pfb-price-range').remove();
        });

        // Open variable modal
        $('#pfb-add-variable, .pfb-add-variable-to-category').on('click', function() {
            $('#pfb-variable-modal-title').text('Add New Variable');
            $('#pfb-variable-form')[0].reset();
            $('#variable_id').val('');
            $('#variable_price_type').val('fixed').trigger('change');

            // Set category if adding from category button
            if ($(this).hasClass('pfb-add-variable-to-category')) {
                const categoryId = $(this).data('category-id');
                $('#variable_category_id').val(categoryId);
            }

            // Clear price ranges
            $('#variable_price_ranges').html(`
                <div class="pfb-price-range">
                    <input type="number" class="pfb-form-control pfb-range-min" placeholder="Min Quantity">
                    <input type="number" class="pfb-form-control pfb-range-max" placeholder="Max Quantity">
                    <input type="number" class="pfb-form-control pfb-range-price" placeholder="Price" step="0.01">
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-range">+</button>
                </div>
            `);

            $('#pfb-variable-modal').show();
        });

        // Open category modal
        $('#pfb-add-category').on('click', function() {
            $('#pfb-category-modal-title').text('Add New Category');
            $('#pfb-category-form')[0].reset();
            $('#category_id').val('');

            $('#pfb-category-modal').show();
        });

        // Close modals
        $('.pfb-modal-close, .pfb-modal-cancel').on('click', function() {
            $('.pfb-modal').hide();
        });

        // Save variable
        $('#pfb-variable-modal .pfb-modal-save').on('click', function() {
            const variableData = {
                id: $('#variable_id').val(),
                category_id: $('#variable_category_id').val(),
                name: $('#variable_name').val(),
                variable_key: $('#variable_key').val(),
                price_type: $('#variable_price_type').val(),
                price_value: $('#variable_price_value').val()
            };

            // Validate form
            if (!variableData.category_id || !variableData.name || !variableData.variable_key) {
                alert('Please fill in all required fields.');
                return;
            }

            // Get price ranges if needed
            if (variableData.price_type === 'range') {
                variableData.price_ranges = [];

                $('.pfb-price-range').each(function() {
                    const $range = $(this);
                    const min = $range.find('.pfb-range-min').val();
                    const max = $range.find('.pfb-range-max').val();
                    const price = $range.find('.pfb-range-price').val();

                    if (min && price) {
                        variableData.price_ranges.push({
                            min: min,
                            max: max || '',
                            price: price
                        });
                    }
                });

                if (variableData.price_ranges.length === 0) {
                    alert('Please add at least one price range.');
                    return;
                }
            }

            // Save variable via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_save_price_variable',
                    nonce: pfb_data.nonce,
                    variable_data: variableData
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Save category
        $('#pfb-category-modal .pfb-modal-save').on('click', function() {
            const categoryData = {
                id: $('#category_id').val(),
                name: $('#category_name').val(),
                description: $('#category_description').val()
            };

            // Validate form
            if (!categoryData.name) {
                alert('Please fill in all required fields.');
                return;
            }

            // Save category via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_save_price_category',
                    nonce: pfb_data.nonce,
                    category_data: categoryData
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Edit variable
        $('.pfb-edit-variable').on('click', function() {
            const variableId = $(this).closest('.pfb-variable-item').data('variable-id');

            // Get variable data via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'GET',
                data: {
                    action: 'pfb_get_price_variable',
                    nonce: pfb_data.nonce,
                    variable_id: variableId
                },
                success: function(response) {
                    if (response.success) {
                        const variable = response.data.variable;

                        $('#pfb-variable-modal-title').text('Edit Variable');
                        $('#variable_id').val(variable.id);
                        $('#variable_category_id').val(variable.category_id);
                        $('#variable_name').val(variable.name);
                        $('#variable_key').val(variable.variable_key);
                        $('#variable_price_type').val(variable.price_type).trigger('change');
                        $('#variable_price_value').val(variable.price_value);

                        // Set price ranges if needed
                        if (variable.price_type === 'range' && variable.price_ranges) {
                            $('#variable_price_ranges').empty();

                            variable.price_ranges.forEach(function(range, index) {
                                const $range = $(`
                                    <div class="pfb-price-range">
                                        <input type="number" class="pfb-form-control pfb-range-min" placeholder="Min Quantity" value="${range.min}">
                                        <input type="number" class="pfb-form-control pfb-range-max" placeholder="Max Quantity" value="${range.max || ''}">
                                        <input type="number" class="pfb-form-control pfb-range-price" placeholder="Price" step="0.01" value="${range.price}">
                                        <button type="button" class="pfb-btn pfb-btn-secondary ${index === 0 ? 'pfb-add-range' : 'pfb-remove-range'}">${index === 0 ? '+' : '-'}</button>
                                    </div>
                                `);

                                $('#variable_price_ranges').append($range);
                            });
                        }

                        $('#pfb-variable-modal').show();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Edit category
        $('.pfb-edit-category').on('click', function() {
            const categoryId = $(this).closest('tr').data('category-id');

            // Get category data via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'GET',
                data: {
                    action: 'pfb_get_price_category',
                    nonce: pfb_data.nonce,
                    category_id: categoryId
                },
                success: function(response) {
                    if (response.success) {
                        const category = response.data.category;

                        $('#pfb-category-modal-title').text('Edit Category');
                        $('#category_id').val(category.id);
                        $('#category_name').val(category.name);
                        $('#category_description').val(category.description);

                        $('#pfb-category-modal').show();
                    } else {
                        alert(response.data.message);
                    }
                }
            });
        });

        // Delete variable
        $('.pfb-delete-variable').on('click', function() {
            if (confirm(pfb_data.i18n.confirm_delete)) {
                const variableId = $(this).closest('.pfb-variable-item').data('variable-id');

                $.ajax({
                    url: pfb_data.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'pfb_delete_price_variable',
                        nonce: pfb_data.nonce,
                        variable_id: variableId
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data.message);
                        }
                    }
                });
            }
        });

        // Delete category
        $('.pfb-delete-category').on('click', function() {
            if ($(this).prop('disabled')) {
                alert('Cannot delete a category that contains variables. Delete the variables first.');
                return;
            }

            if (confirm(pfb_data.i18n.confirm_delete)) {
                const categoryId = $(this).closest('tr').data('category-id');

                $.ajax({
                    url: pfb_data.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'pfb_delete_price_category',
                        nonce: pfb_data.nonce,
                        category_id: categoryId
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data.message);
                        }
                    }
                });
            }
        });
    });
</script>
