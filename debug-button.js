/**
 * Debug script for the calculate button
 * 
 * Add this script to your WordPress page to debug the calculate button issue.
 * You can add it via the browser console or by adding it to your page.
 */
(function($) {
    'use strict';
    
    // Wait for the document to be ready
    $(document).ready(function() {
        console.log('Debug script loaded');
        
        // Check if jQuery is available
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }
        
        // Check if the calculate button exists
        const $calculateButtons = $('.pfb-calculate-button');
        console.log('Found ' + $calculateButtons.length + ' calculate buttons');
        
        if ($calculateButtons.length === 0) {
            console.error('No calculate buttons found');
            return;
        }
        
        // Add a direct click handler to the calculate button
        $calculateButtons.off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked (from debug script)');
            
            const $formContainer = $(this).closest('.pfb-form-container');
            if ($formContainer.length) {
                console.log('Form container found');
                
                const $form = $formContainer.find('.pfb-form');
                if ($form.length) {
                    console.log('Form found, form ID: ' + $form.data('form-id'));
                    
                    // Get all form fields
                    const formData = {};
                    $form.find('input, select').each(function() {
                        const $input = $(this);
                        const name = $input.attr('name');
                        const value = $input.val();
                        
                        if (name && name !== 'undefined' && name !== '') {
                            formData[name] = value;
                        }
                    });
                    
                    console.log('Form data:', formData);
                    
                    // Find the total field
                    const $totalField = $form.find('.pfb-field-total');
                    if ($totalField.length) {
                        console.log('Total field found');
                        $totalField.find('.pfb-total-value').text('Debug: Button works!');
                    } else {
                        console.error('Total field not found');
                    }
                } else {
                    console.error('Form not found in form container');
                }
            } else {
                console.error('Form container not found');
            }
        });
        
        console.log('Debug script initialized');
    });
})(jQuery);
